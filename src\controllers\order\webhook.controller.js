import crypto from 'crypto';
import config from '../../config/environment.config.js';
import planPurchaseModel from '../../models/order/order.modal.js';
import subscriptionModel from '../../models/order/subscription.model.js';
import planDurationOptionModel from '../../models/pricing/planDurationOption.modal.js';
import { PaymentStatus, SubscriptionStatus } from '../../constants/enums.js';
import { verifyWebhookSignature } from '../../utils/razorpayHelper.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import logger from '../../utils/logger.js';

/**
 * Handle Razorpay webhook events
 * @route POST /api/webhooks/razorpay
 * @access Public (but verified)
 */
const handleRazorpayWebhook = asyncHandler(async (req, res) => {
  const signature = req.headers['x-razorpay-signature'];
  const body = JSON.stringify(req.body);

  // Verify webhook signature
  const isValidSignature = verifyWebhookSignature(
    body,
    signature,
    config.razorpay_webhook_secret || config.razorpay_key_secret
  );

  if (!isValidSignature) {
    logger.warn('Invalid Razorpay webhook signature');
    return res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      status: HttpStatus.STATUS.ERROR,
      message: 'Invalid signature',
    });
  }

  const event = req.body;
  logger.info(`Received Razorpay webhook: ${event.event}`);

  try {
    switch (event.event) {
      case 'payment.captured':
        await handlePaymentCaptured(event.payload.payment.entity);
        break;

      case 'payment.failed':
        await handlePaymentFailed(event.payload.payment.entity);
        break;

      case 'order.paid':
        await handleOrderPaid(event.payload.order.entity);
        break;

      default:
        logger.info(`Unhandled webhook event: ${event.event}`);
    }

    res.status(HttpStatus.STATUS_CODE.OK).json({
      status: HttpStatus.STATUS.SUCCESS,
      message: 'Webhook processed successfully',
    });
  } catch (error) {
    logger.error('Error processing webhook:', error);
    res.status(HttpStatus.STATUS_CODE.INTERNAL_SERVER_ERROR).json({
      status: HttpStatus.STATUS.ERROR,
      message: 'Error processing webhook',
    });
  }
});

/**
 * Handle payment captured event
 */
const handlePaymentCaptured = async (payment) => {
  const order = await planPurchaseModel.findOne({
    razorpay_order_id: payment.order_id,
  });

  if (!order) {
    logger.warn(`Order not found for payment: ${payment.id}`);
    return;
  }

  // Update order status
  order.razorpay_payment_id = payment.id;
  order.status = PaymentStatus.SUCCESS;
  order.paymentDate = new Date(payment.created_at * 1000);
  order.rawResponse = payment;
  await order.save();

  // Activate subscription if not already active
  let subscription = await subscriptionModel.findOne({ orderId: order._id });

  if (!subscription) {
    // Create subscription if it doesn't exist
    const duration = await planDurationOptionModel.findById(order.durationId);
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + duration.valueInDays * 24 * 60 * 60 * 1000);

    subscription = new subscriptionModel({
      userId: order.userId,
      planId: order.planId,
      durationId: order.durationId,
      orderId: order._id,
      status: SubscriptionStatus.ACTIVE,
      startDate,
      endDate,
      isActive: true,
      features: duration.features,
    });
    await subscription.save();
  } else if (!subscription.isActive) {
    await subscription.activate();
  }

  logger.info(`Payment captured and subscription activated for order: ${order._id}`);
};

/**
 * Handle payment failed event
 */
const handlePaymentFailed = async (payment) => {
  const order = await planPurchaseModel.findOne({
    razorpay_order_id: payment.order_id,
  });

  if (!order) {
    logger.warn(`Order not found for failed payment: ${payment.id}`);
    return;
  }

  // Update order status
  order.razorpay_payment_id = payment.id;
  order.status = PaymentStatus.FAILED;
  order.rawResponse = payment;
  await order.save();

  // Update subscription status if exists
  const subscription = await subscriptionModel.findOne({ orderId: order._id });
  if (subscription && subscription.status === SubscriptionStatus.PENDING) {
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.isActive = false;
    await subscription.save();
  }

  logger.info(`Payment failed for order: ${order._id}`);
};

/**
 * Handle order paid event
 */
const handleOrderPaid = async (orderData) => {
  const order = await planPurchaseModel.findOne({
    razorpay_order_id: orderData.id,
  });

  if (!order) {
    logger.warn(`Order not found for paid order: ${orderData.id}`);
    return;
  }

  // This event is fired when an order is fully paid
  // We can use this for additional processing if needed
  logger.info(`Order fully paid: ${order._id}`);
};

export { handleRazorpayWebhook };
