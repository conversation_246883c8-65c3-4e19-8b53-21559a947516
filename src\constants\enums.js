/**
 * Enum-like class representing different types of eating preferences.
 * These constants are used to standardize diet categories throughout the application.
 */
export class EatingPreference {
  /** Vegetarian diet: excludes meat, poultry, fish, etc. */
  static VEGETARIAN = 'Vegetarian';

  /** Non-Vegetarian diet: includes meat, poultry, fish, etc. */
  static NON_VEGETARIAN = 'Non-Vegetarian';

  /** Vegan diet: excludes all animal products. */
  static VEGAN = 'Vegan';

  /** Eggitarian diet: vegetarian diet including eggs. */
  static EGGITARIAN = 'Eggitarian';

  /** Pescetarian diet: vegetarian diet including fish. */
  static PESCETARIAN = 'Pescetarian';

  /** Keto diet: high-fat, low-carbohydrate diet. */
  static KETO = 'Keto';

  /** Gluten-Free diet: excludes gluten-containing foods. */
  static GLUTEN_FREE = 'Gluten-Free';

  /** Low-Fat diet: focuses on low fat intake. */
  static LOW_FAT = 'Low-Fat';

  /** Low-Carb diet: focuses on reducing carbohydrate intake. */
  static LOW_CARB = 'Low-Carb';

  /** High-Protein diet: emphasizes high protein consumption. */
  static HIGH_PROTEIN = 'High-Protein';

  /**
   * Immutable array containing primary eating preferences
   * for validation, UI dropdowns, or business logic.
   */
  static ALL = Object.freeze([EatingPreference.VEGETARIAN, EatingPreference.NON_VEGETARIAN, EatingPreference.VEGAN]);
}

Object.freeze(EatingPreference);

/**
 * Enum-like class representing units of pricing models.
 * Useful for defining billing or payment types in plans or services.
 */
export class PricingUnit {
  /** One-time payment unit - single payment for the service/product. */
  static ONE_TIME = 'one-time';

  /** Immutable array of all pricing units for validation or selection. */
  static ALL = Object.freeze([PricingUnit.ONE_TIME]);
}

Object.freeze(PricingUnit);

/**
 * Enum-like class representing the status of payment transactions.
 * Tracks the lifecycle and outcome of payments in the system.
 */
export class PaymentStatus {
  /** Payment has been created/initiated but not completed yet. */
  static CREATED = 'created';

  /** Payment was successful and confirmed. */
  static SUCCESS = 'success';

  /** Payment failed or was declined. */
  static FAILED = 'failed';

  /** Immutable array of all possible payment statuses. */
  static ALL = Object.freeze([PaymentStatus.CREATED, PaymentStatus.SUCCESS, PaymentStatus.FAILED]);
}

Object.freeze(PaymentStatus);

/**
 * Enum-like class representing qualitative sleep quality categories.
 * Used for sleep tracking features to classify sleep health.
 */
export class SleepQuality {
  /** Sleep was excellent in quality and duration. */
  static EXCELLENT = 'excellent';

  /** Sleep was good, with minor disturbances or less duration. */
  static GOOD = 'good';

  /** Sleep was fair; some problems affecting quality. */
  static FAIR = 'fair';

  /** Sleep was poor, inadequate, or severely disturbed. */
  static POOR = 'poor';

  /** Immutable array of all sleep quality ratings. */
  static ALL = Object.freeze([SleepQuality.EXCELLENT, SleepQuality.GOOD, SleepQuality.FAIR, SleepQuality.POOR]);
}

Object.freeze(SleepQuality);

/**
 * Enum-like class representing types of discounts.
 * Used for defining different discount models in coupons or offers.
 */
export class DiscountType {
  /** Percentage-based discount */
  static PERCENTAGE = 'percentage';

  /** Flat amount-based discount */
  static FLAT = 'flat';

  /** Immutable array of all discount types */
  static ALL = Object.freeze([DiscountType.PERCENTAGE, DiscountType.FLAT]);
}

Object.freeze(DiscountType);

export class CouponType {
  /** Public coupon available to all users */
  static PUBLIC = 'public';

  /** Private coupon available to specific users */
  static PRIVATE = 'private';

  /** Immutable array of all coupon types */
  static ALL = Object.freeze([CouponType.PUBLIC, CouponType.PRIVATE]);
}

Object.freeze(CouponType);

/**
 * Enum-like class representing subscription status.
 * Tracks the lifecycle of user subscriptions.
 */
export class SubscriptionStatus {
  /** Subscription is active and valid */
  static ACTIVE = 'active';

  /** Subscription has expired */
  static EXPIRED = 'expired';

  /** Subscription was cancelled by user */
  static CANCELLED = 'cancelled';

  /** Subscription is pending payment */
  static PENDING = 'pending';

  /** Immutable array of all subscription statuses */
  static ALL = Object.freeze([
    SubscriptionStatus.ACTIVE,
    SubscriptionStatus.EXPIRED,
    SubscriptionStatus.CANCELLED,
    SubscriptionStatus.PENDING,
  ]);
}

Object.freeze(SubscriptionStatus);
