import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';

import { getClientProfile, onboardingProcess, updateClientDetails } from '../../controllers/clients.controller.js';
import { getUserSettings, updateUserSettings } from '../../controllers/clientsSettings.controller.js';

const router = express.Router();

// Apply authentication and authorization middleware to all client-specific routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   POST /api/clients/onboarding-process
 * @desc    Submit onboarding data during first-time setup
 * @access  Client only
 */
router.post('/onboarding-process', onboardingProcess);

/**
 * @route   GET /api/clients/me
 * @desc    Retrieve authenticated client's full profile
 * @access  Client only
 */
router.get('/me', getClientProfile);

/**
 * @route   PUT /api/clients/me
 * @desc    Update authenticated client’s profile information
 * @access  Client only
 */
router.put('/me', updateClientDetails);

/**
 * @route   GET /api/clients/me/settings
 * @desc    Get client-specific user settings (preferences, flags, etc.)
 * @access  Client only
 */
router.get('/me/settings', getUserSettings);

/**
 * @route   PATCH /api/clients/me/settings
 * @desc    Update client-specific user settings (partial update)
 * @access  Client only
 */
router.patch('/me/settings', updateUserSettings);

export default router;
