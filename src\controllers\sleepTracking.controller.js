import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import SleepTrackingService from '../services/sleepTracking.service.js';
import successResponse from '../utils/successResponse.js';

const getSleepRecords = asyncHandler(async (req, res) => {
  const sleepRecords = await SleepTrackingService.getSleepRecords(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Sleep records retrieved successfully', sleepRecords);
});

const getSleepRecordById = asyncHandler(async (req, res) => {
  const sleepRecord = await SleepTrackingService.getSleepRecordById(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Sleep record retrieved successfully', sleepRecord);
});

const updateSleepRecord = asyncHandler(async (req, res) => {
  const updatedSleepRecord = await SleepTrackingService.updateSleepRecord(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Sleep record updated successfully', updatedSleepRecord);
});

const getSleepRecordsRange = asyncHandler(async (req, res) => {
  const sleepRecords = await SleepTrackingService.getSleepRecordsRange(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Sleep records retrieved successfully', sleepRecords);
});

export { getSleepRecords, getSleepRecordById, updateSleepRecord, getSleepRecordsRange };
