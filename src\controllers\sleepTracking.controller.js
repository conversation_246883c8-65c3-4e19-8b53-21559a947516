import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import SleepTrackingService from '../services/sleepTracking.service.js';
import { ValidationMessages } from '../constants/messages.js';

const getSleepRecords = asyncHandler(async (req, res) => {
  const sleepRecords = await SleepTrackingService.getSleepRecords(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: sleepRecords,
  });
});

const getSleepRecordById = asyncHandler(async (req, res) => {
  const sleepRecord = await SleepTrackingService.getSleepRecordById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: sleepRecord,
  });
});

const updateSleepRecord = asyncHandler(async (req, res) => {
  const updatedSleepRecord = await SleepTrackingService.updateSleepRecord(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: updatedSleepRecord,
  });
});

const getSleepRecordsRange = asyncHandler(async (req, res) => {
  const sleepRecords = await SleepTrackingService.getSleepRecordsRange(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: sleepRecords,
  });
});

export { getSleepRecords, getSleepRecordById, updateSleepRecord, getSleepRecordsRange };
