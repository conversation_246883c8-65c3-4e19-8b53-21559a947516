import mongoose from 'mongoose';

const userRefreshTokenSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  blacklisted: {
    type: Boolean,
    default: false,
  },
  token: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: '30d',
  },
});

// Index for faster queries
userRefreshTokenSchema.index({ userId: 1 });

const UserRefreshTokenModel = mongoose.model('UserRefreshToken', userRefreshTokenSchema, 'user_refresh_tokens');

export default UserRefreshTokenModel;
