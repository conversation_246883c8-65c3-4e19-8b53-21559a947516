import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import ClientService from '../services/clients.service.js';
import { ValidationMessages } from '../constants/messages.js';

const onboardingProcess = asyncHandler(async (req, res) => {
  const clientData = await ClientService.onboardingProcess(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Client onboarding process completed successfully',
    data: clientData,
  });
});

const createClientByAdmin = asyncHandler(async (req, res) => {
  const createdClient = await ClientService.createClientByAdmin(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Client account created successfully',
    data: createdClient,
  });
});

const getClientProfile = asyncHandler(async (req, res) => {
  const clientProfile = await ClientService.getClientProfile(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: clientProfile,
  });
});

const getClientsList = asyncHandler(async (req, res) => {
  const getAllClients = await ClientService.getClientsList(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    ...getAllClients,
  });
});

const getClientById = asyncHandler(async (req, res) => {
  const clientDetails = await ClientService.getClientById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: clientDetails,
  });
});

const updateClientDetails = asyncHandler(async (req, res) => {
  const updatedClientDetails = await ClientService.updateClientDetails(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: updatedClientDetails,
  });
});

const updateClientByAdmin = asyncHandler(async (req, res) => {
  const updatedClientResponse = await ClientService.updateClientByAdmin(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: updatedClientResponse,
  });
});

const deactivateClientByAdmin = asyncHandler(async (req, res) => {
  const deactivatedClientResponse = await ClientService.deactivateClientByAdmin(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Client account deactivated successfully',
    data: deactivatedClientResponse,
  });
});

const activateClientByAdmin = asyncHandler(async (req, res) => {
  const activatedClientResponse = await ClientService.activateClientByAdmin(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Client account activated successfully',
    data: activatedClientResponse,
  });
});

const deleteClientByAdmin = asyncHandler(async (req, res) => {
  const deletedClientResponse = await ClientService.deleteClientByAdmin(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Client account deactivated successfully',
    data: deletedClientResponse,
  });
});

export {
  onboardingProcess,
  createClientByAdmin,
  getClientsList,
  getClientById,
  getClientProfile,
  updateClientDetails,
  updateClientByAdmin,
  deactivateClientByAdmin,
  activateClientByAdmin,
  deleteClientByAdmin,
};
