import asyncHandler from '../utils/asyncHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import ClientService from '../services/clients.service.js';
import { ValidationMessages } from '../constants/messages.js';
import successResponse from '../utils/successResponse.js';

const onboardingProcess = asyncHandler(async (req, res) => {
  const clientData = await ClientService.onboardingProcess(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Onboarding process completed successfully', clientData);
});

const createClientByAdmin = asyncHandler(async (req, res) => {
  const createdClient = await ClientService.createClientByAdmin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.CREATED, 'Client account created successfully', createdClient);
});

const getClientProfile = asyncHandler(async (req, res) => {
  const clientProfile = await ClientService.getClientProfile(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client profile retrieved successfully', clientProfile);
});

const getClientsList = asyncHandler(async (req, res) => {
  const getAllClients = await ClientService.getClientsList(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Clients list retrieved successfully', getAllClients);
});

const getClientById = asyncHandler(async (req, res) => {
  const clientDetails = await ClientService.getClientById(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client details retrieved successfully', clientDetails);
});

const updateClientDetails = asyncHandler(async (req, res) => {
  const updatedClientDetails = await ClientService.updateClientDetails(req, res);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client details updated successfully', updatedClientDetails);
});

const updateClientByAdmin = asyncHandler(async (req, res) => {
  const updatedClientResponse = await ClientService.updateClientByAdmin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client details updated successfully', updatedClientResponse);
});

const deactivateClientByAdmin = asyncHandler(async (req, res) => {
  const deactivatedClientResponse = await ClientService.deactivateClientByAdmin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client account deactivated successfully', deactivatedClientResponse);
});

const activateClientByAdmin = asyncHandler(async (req, res) => {
  const activatedClientResponse = await ClientService.activateClientByAdmin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client account activated successfully', activatedClientResponse);
});

const deleteClientByAdmin = asyncHandler(async (req, res) => {
  const deletedClientResponse = await ClientService.deleteClientByAdmin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Client account deleted successfully', deletedClientResponse);
});

export {
  onboardingProcess,
  createClientByAdmin,
  getClientsList,
  getClientById,
  getClientProfile,
  updateClientDetails,
  updateClientByAdmin,
  deactivateClientByAdmin,
  activateClientByAdmin,
  deleteClientByAdmin,
};
