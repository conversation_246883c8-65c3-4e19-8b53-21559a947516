import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';
import logger from '../utils/logger.js';

/**
 * Centralized Express error handler middleware.
 */
const handleError = (err, req, res, next) => {
  err.statusCode = err.statusCode || HttpStatus.STATUS_CODE.SERVER_ERROR;
  err.status = err.status || HttpStatus.STATUS.ERROR;

  if (process.env.NODE_ENV === 'development') {
    // In development, return full error details
    return res.status(err.statusCode).json({
      status: err.status,
      error: err,
      message: err.message,
      stack: err.stack, // Include stack trace for debugging
    });
  }

  if (err.isOperational) {
    // Return structured error response for operational errors
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
    });
  }

  logger.error('Unhandled Error:', err);

  return res.status(HttpStatus.STATUS_CODE.SERVER_ERROR).json({
    status: HttpStatus.STATUS.ERROR,
    message: ValidationMessages.GENERAL.SERVER_ERROR,
  });
};

export { handleError };
