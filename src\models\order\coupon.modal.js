import mongoose from 'mongoose';
import { CouponType, DiscountType } from '../../constants/enums.js';

const couponSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
    },
    couponType: {
      type: String,
      enum: CouponType.ALL,
      default: CouponType.PUBLIC,
      required: true,
    },
    discountType: {
      type: String,
      enum: DiscountType.ALL,
      default: DiscountType.PERCENTAGE,
      required: true,
    },
    discountValue: {
      type: Number,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    expiresAt: {
      type: Date,
      default: null,
    },
    allowedUsers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
      },
    ],
    usageLimitPerUser: {
      type: Number,
      default: 1,
    },
    usedBy: [
      {
        userId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User',
        },
        usageCount: {
          type: Number,
          default: 0,
        },
        ipAddress: {
          type: String,
          default: null,
        },
        device: {
          type: String,
          default: null,
        },
        usedAt: {
          type: Date,
          default: null,
        },
      },
    ],
    customSort: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

export default mongoose.models.Coupon || mongoose.model('Coupon', couponSchema, 'coupons');
