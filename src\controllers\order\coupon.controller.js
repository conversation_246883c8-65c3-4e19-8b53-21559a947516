import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import CouponService from '../../services/order/coupon.service.js';

const createCoupon = asyncHandler(async (req, res) => {
  const couponData = await CouponService.createCoupon(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon created successfully',
    data: couponData,
  });
});

const getCoupons = asyncHandler(async (req, res) => {
  const coupons = await CouponService.getCoupons(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupons retrieved successfully',
    data: coupons,
  });
});

const getCouponById = asyncHandler(async (req, res) => {
  const coupon = await CouponService.getCouponById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon retrieved successfully',
    data: coupon,
  });
});

const updateCoupon = asyncHandler(async (req, res) => {
  const updatedCoupon = await CouponService.updateCoupon(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon updated successfully',
    data: updatedCoupon,
  });
});

const activateCoupon = asyncHandler(async (req, res) => {
  const activatedCoupon = await CouponService.activateCoupon(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon activated successfully',
    data: activatedCoupon,
  });
});

const deactivateCoupon = asyncHandler(async (req, res) => {
  const deactivatedCoupon = await CouponService.deactivateCoupon(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon deactivated successfully',
    data: deactivatedCoupon,
  });
});

const deleteCoupon = asyncHandler(async (req, res) => {
  const deletedCoupon = await CouponService.deleteCoupon(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Coupon deleted successfully',
    data: deletedCoupon,
  });
});

export {
  createCoupon,
  getCoupons,
  getCouponById,
  updateCoupon,
  validateCoupon,
  deleteCoupon,
  activateCoupon,
  deactivateCoupon,
};
