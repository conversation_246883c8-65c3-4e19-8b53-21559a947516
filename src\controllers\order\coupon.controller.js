import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import CouponService from '../../services/order/coupon.service.js';
import successResponse from '../../utils/successResponse.js';

/**
 * Create a new coupon
 * @route POST /api/coupons
 * @access Admin
 */
const createCoupon = asyncHandler(async (req, res) => {
  const couponData = await CouponService.createCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.CREATED, 'Coupon created successfully', couponData);
});

/**
 * Get all coupons
 * @route GET /api/coupons
 * @access Admin
 */
const getCoupons = asyncHandler(async (req, res) => {
  const coupons = await CouponService.getCoupons(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupons retrieved successfully', coupons);
});

/**
 * Get a specific coupon by ID
 * @route GET /api/coupons/:id
 * @access Admin
 */
const getCouponById = asyncHandler(async (req, res) => {
  const coupon = await CouponService.getCouponById(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon retrieved successfully', coupon);
});

/**
 * Update a specific coupon by ID
 * @route PUT /api/coupons/:id
 * @access Admin
 */
const updateCoupon = asyncHandler(async (req, res) => {
  const updatedCoupon = await CouponService.updateCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon updated successfully', updatedCoupon);
});

/**
 * Activate a specific coupon by ID
 * @route PATCH /api/coupons/:id/activate
 * @access Admin
 */
const activateCoupon = asyncHandler(async (req, res) => {
  const activatedCoupon = await CouponService.activateCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon activated successfully', activatedCoupon);
});

/**
 * Deactivate a specific coupon by ID
 * @route PATCH /api/coupons/:id/deactivate
 * @access Admin
 */
const deactivateCoupon = asyncHandler(async (req, res) => {
  const deactivatedCoupon = await CouponService.deactivateCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon deactivated successfully', deactivatedCoupon);
});

/**
 * Delete a specific coupon by ID
 * @route DELETE /api/coupons/:id
 * @access Admin
 */
const deleteCoupon = asyncHandler(async (req, res) => {
  const deletedCoupon = await CouponService.deleteCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon deleted successfully', deletedCoupon);
});

/**
 * Validate a coupon code
 * @route POST /api/coupons/validate
 * @access Client
 */
const validateCoupon = asyncHandler(async (req, res) => {
  const validatedCoupon = await CouponService.validateCoupon(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon validated successfully', validatedCoupon);
});

export {
  createCoupon,
  getCoupons,
  getCouponById,
  updateCoupon,
  validateCoupon,
  deleteCoupon,
  activateCoupon,
  deactivateCoupon,
};
