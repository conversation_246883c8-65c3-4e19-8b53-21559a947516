import express from 'express';

// Import sub-route modules for the pricing domain
import featureRoutes from './planFeature.routes.js';
import durationRoutes from './planDurationOption.routes.js';
import planRoutes from './pricingPlans.routes.js';

const router = express.Router();

/**
 * @route   /api/pricing/features
 * @desc    Handles CRUD operations for plan features
 */
router.use('/features', featureRoutes);

/**
 * @route   /api/pricing/durations
 * @desc    Handles CRUD operations for plan duration options
 */
router.use('/durations', durationRoutes);

/**
 * @route   /api/pricing/plans
 * @desc    Handles CRUD operations for full pricing plans
 */
router.use('/plans', planRoutes);

export default router;
