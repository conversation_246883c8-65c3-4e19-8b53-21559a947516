import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import passport from 'passport';
import chalk from 'chalk';
import './config/passport.config.js';
import dbConnection from './config/database.js';
import config from './config/environment.config.js';
import logger from './utils/logger.js';
import { handleError } from './middlewares/errorHandler.js';
import { sanitizeRequest } from './middlewares/sanitizeRequest.js';
import { ValidationMessages } from './constants/messages.js';
import { Constants } from './constants/general.js';
import rateLimiter from './middlewares/rateLimiter.js';
import { AppError } from './utils/errorHandler.js';
import { HttpStatus } from './constants/httpStatus.js';
import httpLogger from './middlewares/httpLogger.js';
import authRoutes from './routes/auth.routes.js';
import clientClientsRouter from './routes/client/clients.routes.js';
import clientAdminRoutes from './routes/admin/client.admin.routes.js';
import clientSleepRoutes from './routes/client/client.sleep.routes.js';
import pricingRoutes from './routes/admin/pricing/index.js';
import dashboardOverviewRoutes from './routes/admin/dashboard.overview.routes.js';
import couponAdminRoutes from './routes/admin/coupon.admin.routes.js';
import couponRoutes from './routes/client/coupon.routes.js';
import paymentRoutes from './routes/client/payment.routes.js';
import subscriptionRoutes from './routes/client/subscription.routes.js';
import paymentAdminRoutes from './routes/admin/payment.admin.routes.js';
import webhookRoutes from './routes/webhook.routes.js';
import successResponse from './utils/successResponse.js';

const app = express();
const port = process.env.PORT || 8000;

// Middleware
app.use(
  cors({
    origin: config.frontend_host, // Allow requests from the frontend host
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    credentials: true, // Allow credentials (cookies, authorization headers, etc.)
    exposedHeaders: [Constants.HEADERS.AUTHORIZATION], // Expose Authorization header to the frontend
    maxAge: 3600, // Cache preflight response for 1 hour
  })
);

// Apply rate limiting to all routes
app.use(httpLogger);
app.use(rateLimiter(500, 5 * 60 * 1000)); // 500 requests per 5 minutes
app.use(express.json({ limit: '16kb' }));
app.use(express.urlencoded({ extended: true, limit: '16kb' }));
app.use(passport.initialize());
app.use(cookieParser());
app.use(sanitizeRequest);
app.use(helmet());

// Database Connection
await dbConnection(config.database_url);

// Serve static files from public directory
app.use(express.static('public'));

// Health check route
app.get('/api-health', (req, res) => {
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Coupon retrieved successfully');

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.SERVER.HEALTHY,
  });
});

// Welcome page route - serve the HTML file
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'public' });
});

// Authentication Routes
app.use('/api/auth', authRoutes);

// Client Routes
app.use('/api/clients', clientClientsRouter);
app.use('/api/clients/sleep-tracking', clientSleepRoutes);
app.use('/api/coupons', couponRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/subscriptions', subscriptionRoutes);

// Admin Routes
app.use('/api/admin/clients', clientAdminRoutes);
app.use('/api/admin/dashboard', dashboardOverviewRoutes);
app.use('/api/admin/coupons', couponAdminRoutes);
app.use('/api/admin', paymentAdminRoutes);
app.use('/api/pricing', pricingRoutes);

// Webhook Routes (should be before other middleware that might interfere)
app.use('/api/webhooks', webhookRoutes);

// 404 handler for undefined routes
app.use((req, res, next) => {
  next(new AppError(ValidationMessages.GENERAL.ROUTE(req.originalUrl), HttpStatus.STATUS_CODE.NOT_FOUND));
});

// Global error handler
app.use(handleError);

// Start server
app.listen(port, () => {
  logger.info(`${chalk.cyan(ValidationMessages.SERVER.STARTED(port))}`);
});

// Handle unhandled promise rejections
process.on(HttpStatus.STATUS.UNHANDLED_REJECTION, (err) => {
  logger.error(ValidationMessages.SERVER.UNHANDLED);
  logger.error(err);
  process.exit(1);
});
