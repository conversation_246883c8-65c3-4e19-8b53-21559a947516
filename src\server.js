import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import chalk from 'chalk';
import passport from 'passport';

import config from './config/environment.config.js';
import logger from './utils/logger.js';
import dbConnection from './config/database.js';
import { ValidationMessages } from './constants/messages.js';
import { HttpStatus } from './constants/httpStatus.js';
import { AppError } from './utils/errorHandler.js';

import setupMiddlewares from './middlewares/index.js';
import registerRoutes from './routes/index.js';

// Initialize App
const app = express();
const port = process.env.PORT || 8000;

// Database Connection
await dbConnection(config.database_url);

// Setup Middlewares
setupMiddlewares(app);

// Passport Configuration
import './config/passport.config.js';
app.use(passport.initialize());

// Serve static files
app.use(express.static('public'));

// Welcome page route
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'public' });
});

// Register API routes
registerRoutes(app);

// Catch-all for undefined routes
app.use((req, res, next) => {
  next(new AppError(ValidationMessages.GENERAL.ROUTE(req.originalUrl), HttpStatus.STATUS_CODE.NOT_FOUND));
});

// Global error handler
import { handleError } from './middlewares/errorHandler.js';
app.use(handleError);

// Start server
app.listen(port, () => {
  logger.info(`${chalk.cyan(ValidationMessages.SERVER.STARTED(port))}`);
});

// Handle unhandled promise rejections
process.on(HttpStatus.STATUS.UNHANDLED_REJECTION, (err) => {
  logger.error(ValidationMessages.SERVER.UNHANDLED);
  logger.error(err);
  process.exit(1);
});
