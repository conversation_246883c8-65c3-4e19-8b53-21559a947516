import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';

/**
 * Base class for all application-specific operational errors.
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? HttpStatus.STATUS.FAILED : HttpStatus.STATUS.ERROR;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Specific error class for validation errors.
 */
class ValidationError extends AppError {
  constructor(message = ValidationMessages.GENERAL.INVALID_REQUEST) {
    super(message, HttpStatus.STATUS_CODE.BAD_REQUEST);
  }
}

export class ValidateRequiredFieldsError extends AppError {
  constructor(message = ValidationMessages.GENERAL.MISSING_FIELDS) {
    super(message, HttpStatus.STATUS_CODE.BAD_REQUEST);
  }
}

/**
 * Specific error class for resource not found errors.
 */
class NotFoundError extends AppError {
  constructor(message = ValidationMessages.GENERAL.NOT_FOUND) {
    super(message, HttpStatus.STATUS_CODE.NOT_FOUND);
  }
}

/**
 * Specific error class for unauthorized access.
 */
class UnauthorizedError extends AppError {
  constructor(message = ValidationMessages.GENERAL.UNAUTHORIZED) {
    super(message, HttpStatus.STATUS_CODE.UNAUTHORIZED);
  }
}

class ForbiddenError extends AppError {
  constructor(message = ValidationMessages.GENERAL.FORBIDDEN) {
    super(message, HttpStatus.STATUS_CODE.FORBIDDEN);
  }
}

export { AppError, ValidationError, NotFoundError, UnauthorizedError, ForbiddenError };
