/**
 * HttpStatus class encapsulates common HTTP status codes and related status messages.
 * This provides a centralized, immutable source for HTTP response status management,
 * promoting consistency and reducing magic numbers or strings across the codebase.
 */
export class HttpStatus {
  /**
   * HTTP status codes commonly used in API responses.
   * These codes follow standard HTTP protocol status codes.
   */
  static STATUS_CODE = Object.freeze({
    /** Successful HTTP request. */
    OK: 200,

    /** Resource successfully created. */
    CREATED: 201,

    /** Successful request with no content to return. */
    NO_CONTENT: 204,

    /** Multiple choices available for the resource. */
    MULTIPLE_CHOICES: 300,

    /** The server could not understand the request due to invalid syntax. */
    BAD_REQUEST: 400,

    /** Authentication is required and has failed or not been provided. */
    UNAUTHORIZED: 401,

    /** The client does not have access rights to the content. */
    FORBIDDEN: 403,

    /** The server can not find requested resource. */
    NOT_FOUND: 404,

    /** Request conflicts with current state of the server/resource. */
    CONFLICT: 409,

    /** The request was well-formed but was unable to be followed due to semantic errors. */
    UNPROCESSABLE_ENTITY: 422,

    /** The user has sent too many requests in a given amount of time ("rate limiting"). */
    TOO_MANY_REQUEST: 429,

    /** Internal server error occurred. */
    SERVER_ERROR: 500,
  });

  /**
   * Standardized status messages corresponding to API responses.
   * These strings can be used to maintain uniform API response formats.
   */
  static STATUS = Object.freeze({
    /** Request was successful. */
    SUCCESS: 'Success',

    /** General error message for failed requests. */
    ERROR: 'Error',

    /** The operation failed to complete as expected. */
    FAILED: 'Failed',

    /** The request was unauthorized due to invalid or missing credentials. */
    UNAUTHORIZED: 'Unauthorized',

    /** Validation error occurred with input or data. */
    VALIDATION_ERROR: 'ValidationError',

    /** Represents unhandled promise rejection errors in the application. */
    UNHANDELED_REJECTION: 'unhandledRejection',
  });
}

// Freeze the HttpStatus class to prevent modifications
Object.freeze(HttpStatus);
