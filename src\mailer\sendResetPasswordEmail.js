import transporter from '../config/email.config.js';
import config from '../config/environment.config.js';
import resetPasswordTemplate from './template/resetPasswordTemplate.js';

// Send OTP email
const sendResetPasswordEmail = async (user, otp) => {
  const mailOptions = {
    from: config.email_from,
    to: user.email,
    subject: 'Password Reset OTP',
    html: resetPasswordTemplate(user, otp),
  };

  await transporter.sendMail(mailOptions);
};

export default sendResetPasswordEmail;
