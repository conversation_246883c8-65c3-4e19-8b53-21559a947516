# 🧪 Postman Testing Guide - Food for Soul Tech API

## 📋 Overview

This guide provides comprehensive instructions for testing the Food for Soul Tech API using Postman. The API has been organized into separate collections for better management and testing.

## 📁 Collection Files

1. **`Food_for_Soul_Tech_API.postman_collection.json`** - Main collection (Health Check, Auth, Public, Client routes)
2. **`Food_for_Soul_Tech_Admin_API.postman_collection.json`** - Admin-specific routes
3. **`Food_for_Soul_Tech_Pricing_API.postman_collection.json`** - Pricing management routes

## 🚀 Quick Start

### 1. Import Collections

1. Open Postman
2. Click **Import** button
3. Select **File** tab
4. Choose all three JSON collection files
5. Click **Import**

### 2. Set Up Environment

1. Create a new environment called "Food for Soul Tech"
2. Add the following variables:

| Variable | Initial Value | Current Value |
|----------|---------------|---------------|
| `baseUrl` | `http://localhost:8000` | `http://localhost:8000` |
| `accessToken` | `` | `` |
| `refreshToken` | `` | `` |
| `adminToken` | `` | `` |

### 3. Start the Server

Make sure your server is running:
```bash
cd food-for-soul-tech-node-express
npm start
# or
node src/server.js
```

## 🔐 Authentication Flow

### For Client Testing:

1. **Register a new user** (optional):
   - Use `POST /api/auth/signup`
   - The response will automatically set `accessToken` and `refreshToken`

2. **Login as client**:
   - Use `POST /api/auth/login`
   - The response will automatically set `accessToken` and `refreshToken`

3. **Test client routes**:
   - All client routes will now use the stored `accessToken`

### For Admin Testing:

1. **Login as admin**:
   - Use `POST /api/auth/login/admin`
   - The response will automatically set `adminToken`

2. **Test admin routes**:
   - All admin routes will use the stored `adminToken`

## 📊 Testing Workflow

### 1. Health Check Tests
Start with these to ensure the server is running:

```
✅ Basic Health Check (GET /api-health)
✅ Detailed Health Check (GET /api/health)
✅ Extended Health Check (GET /api/health/detailed)
```

### 2. Public API Tests
Test public endpoints (no authentication required):

```
✅ Get Pricing Plans (GET /api/pricing-plans)
```

### 3. Authentication Tests
Test all authentication flows:

```
✅ Register (POST /api/auth/signup)
✅ Login (POST /api/auth/login)
✅ Admin Login (POST /api/auth/login/admin)
✅ Forgot Password (POST /api/auth/forgot-password)
✅ Reset Password (POST /api/auth/reset-password)
✅ Change Password (POST /api/auth/change-password)
✅ Refresh Token (POST /api/auth/refresh-token)
✅ Verify Email (POST /api/auth/verify-email)
✅ Send Verification Email (POST /api/auth/send-verification-email)
✅ Logout (POST /api/auth/logout)
```

### 4. Client API Tests
Test client-specific functionality (requires client authentication):

#### Profile Management:
```
✅ Get Client Profile (GET /api/clients/profile/me)
✅ Client Onboarding (POST /api/clients/profile/onboarding)
✅ Update Client Profile (PUT /api/clients/profile/me)
✅ Get User Settings (GET /api/clients/profile/settings)
✅ Update User Settings (PATCH /api/clients/profile/settings)
```

#### Sleep Tracking:
```
✅ Get Sleep Records (GET /api/clients/sleep-tracking)
✅ Get Sleep Records by Date Range (GET /api/clients/sleep-tracking/range)
✅ Update Sleep Record (PATCH /api/clients/sleep-tracking)
```

#### Payments:
```
✅ Create Payment Order (POST /api/clients/payments/create-order)
✅ Verify Payment (POST /api/clients/payments/verify)
✅ Get Payment History (GET /api/clients/payments/history)
✅ Get Payment Details (GET /api/clients/payments/:id)
```

#### Subscriptions:
```
✅ Get Active Subscription (GET /api/clients/subscriptions/active)
✅ Get Subscription History (GET /api/clients/subscriptions/history)
✅ Get Subscription Details (GET /api/clients/subscriptions/:subscriptionId)
✅ Cancel Subscription (POST /api/clients/subscriptions/cancel)
```

#### Coupons:
```
✅ Validate Coupon (POST /api/clients/coupons/validate)
```

### 5. Admin API Tests
Test admin-specific functionality (requires admin authentication):

#### Dashboard:
```
✅ Get Dashboard Overview (GET /api/admin/dashboard)
✅ Get Dashboard Stats (GET /api/admin/dashboard/stats)
```

#### Client Management:
```
✅ Create Client (POST /api/admin/clients)
✅ Get All Clients (GET /api/admin/clients)
✅ Get Client by ID (GET /api/admin/clients/:id)
✅ Update Client (PUT /api/admin/clients/:id)
✅ Activate Client (PATCH /api/admin/clients/:id/activate)
✅ Deactivate Client (PATCH /api/admin/clients/:id/deactivate)
✅ Delete Client (DELETE /api/admin/clients/:id)
✅ Get Client Sleep Record (GET /api/admin/clients/:id/sleep-records/:recordId)
```

#### Payment Management:
```
✅ Get All Payments (GET /api/admin/payments)
✅ Get Payment Statistics (GET /api/admin/payments/stats)
✅ Get Payment Details (GET /api/admin/payments/:id)
```

#### Subscription Management:
```
✅ Get All Subscriptions (GET /api/admin/subscriptions)
✅ Get Subscription Statistics (GET /api/admin/subscriptions/stats)
✅ Update Expired Subscriptions (POST /api/admin/subscriptions/update-expired)
✅ Get Subscription Details (GET /api/admin/subscriptions/:id)
✅ Toggle Subscription Status (PATCH /api/admin/subscriptions/:id/toggle)
```

#### Coupon Management:
```
✅ Create Coupon (POST /api/admin/coupons)
✅ Get All Coupons (GET /api/admin/coupons)
✅ Get Coupon by ID (GET /api/admin/coupons/:id)
✅ Update Coupon (PUT /api/admin/coupons/:id)
✅ Activate Coupon (PATCH /api/admin/coupons/:id/activate)
✅ Deactivate Coupon (PATCH /api/admin/coupons/:id/deactivate)
✅ Delete Coupon (DELETE /api/admin/coupons/:id)
```

### 6. Pricing Management Tests
Test pricing management functionality (requires admin authentication):

#### Plan Features:
```
✅ Create Plan Feature (POST /api/admin/pricing/features)
✅ Get All Plan Features (GET /api/admin/pricing/features)
✅ Get Plan Feature by ID (GET /api/admin/pricing/features/:id)
✅ Update Plan Feature (PUT /api/admin/pricing/features/:id)
✅ Delete Plan Feature (DELETE /api/admin/pricing/features/:id)
```

#### Plan Duration Options:
```
✅ Create Duration Option (POST /api/admin/pricing/durations)
✅ Get All Duration Options (GET /api/admin/pricing/durations)
✅ Get Duration Option by ID (GET /api/admin/pricing/durations/:id)
✅ Update Duration Option (PUT /api/admin/pricing/durations/:id)
✅ Delete Duration Option (DELETE /api/admin/pricing/durations/:id)
```

#### Pricing Plans:
```
✅ Create Pricing Plan (POST /api/admin/pricing/plans)
✅ Get All Pricing Plans (Admin) (GET /api/admin/pricing/plans)
✅ Get Pricing Plan by ID (GET /api/admin/pricing/plans/:id)
✅ Update Pricing Plan (PUT /api/admin/pricing/plans/:id)
✅ Delete Pricing Plan (DELETE /api/admin/pricing/plans/:id)
```

## 🔧 Testing Tips

### 1. Automatic Token Management
The collections include scripts that automatically:
- Extract and store access tokens from login responses
- Set tokens in environment variables
- Use tokens in subsequent requests

### 2. Variable Substitution
Replace placeholder values in requests:
- `client_id_here` → Actual client ID from database
- `plan_id_here` → Actual plan ID from database
- `payment_id_here` → Actual payment ID from responses

### 3. Testing Order
Follow this order for comprehensive testing:
1. Health checks
2. Authentication
3. Public endpoints
4. Client endpoints (after client login)
5. Admin endpoints (after admin login)

### 4. Error Testing
Test error scenarios:
- Invalid credentials
- Missing required fields
- Invalid IDs
- Unauthorized access
- Expired tokens

## 📝 Sample Test Data

### User Registration:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Test",
  "lastName": "User",
  "phoneNumber": "+**********"
}
```

### Admin Login:
```json
{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

### Plan Feature Creation:
```json
{
  "name": "Premium Support",
  "description": "24/7 customer support",
  "slug": "premium-support"
}
```

### Duration Option Creation:
```json
{
  "label": "6 Months",
  "valueInDays": 180,
  "price": 299,
  "currency": "INR",
  "paymentType": "one_time",
  "features": ["feature_id_1", "feature_id_2"]
}
```

## 🐛 Troubleshooting

### Common Issues:

1. **401 Unauthorized**
   - Check if token is set in environment
   - Verify token hasn't expired
   - Ensure correct role (CLIENT vs ADMIN)

2. **404 Not Found**
   - Verify server is running on correct port
   - Check endpoint URL spelling
   - Ensure route exists in current version

3. **422 Unprocessable Entity**
   - Check request body format
   - Verify required fields are included
   - Validate data types

4. **500 Internal Server Error**
   - Check server logs
   - Verify database connection
   - Check for missing environment variables

### Debug Steps:
1. Check server console for errors
2. Verify environment variables are set
3. Test with simpler endpoints first
4. Use Postman Console to debug requests

## 📞 Support

For testing support:
- Check server logs for detailed error messages
- Verify all environment variables are configured
- Ensure database is connected and accessible
- Review API documentation for correct request formats

**Server Status**: ✅ http://localhost:8000  
**Health Check**: ✅ http://localhost:8000/api-health  
**Documentation**: 📖 API_DOCUMENTATION.md
