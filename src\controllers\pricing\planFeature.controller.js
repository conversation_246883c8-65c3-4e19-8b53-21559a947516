import PlanFeatureService from '../../services/pricing/planFeature.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';

const createPlanFeature = asyncHandler(async (req, res) => {
  const planFeatureData = await PlanFeatureService.createPlanFeature(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan feature created successfully',
    data: planFeatureData,
  });
});

const getPlanFeatures = asyncHandler(async (req, res) => {
  const planFeatures = await PlanFeatureService.getPlanFeatures(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan features retrieved successfully',
    data: planFeatures,
  });
});

const getPlanFeatureById = asyncHandler(async (req, res) => {
  const planFeature = await PlanFeatureService.getPlanFeatureById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan feature retrieved successfully',
    data: planFeature,
  });
});

const updatePlanFeature = asyncHandler(async (req, res) => {
  const updatedPlanFeature = await PlanFeatureService.updatePlanFeature(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan feature updated successfully',
    data: updatedPlanFeature,
  });
});

const deletePlanFeature = asyncHandler(async (req, res) => {
  const deletedPlanFeature = await PlanFeatureService.deletePlanFeature(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan feature deleted successfully',
    data: deletedPlanFeature,
  });
});

export { createPlanFeature, getPlanFeatures, getPlanFeatureById, updatePlanFeature, deletePlanFeature };
