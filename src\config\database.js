import dotenv from 'dotenv';
dotenv.config();

import mongoose from 'mongoose';
import chalk from 'chalk';
import config from './environment.config.js';
import logger from '../utils/logger.js';
import { ValidationMessages } from '../constants/messages.js';

const dbConnection = async (DATABASE_URL) => {
  try {
    await mongoose.connect(DATABASE_URL, {
      dbName: config.database_name,
      // Additional options for improved stability and monitoring can be added here
      // e.g., useNewUrlParser: true, useUnifiedTopology: true (though defaults may vary by mongoose version)
    });

    logger.info(
      `${chalk.cyan(ValidationMessages.SERVER.DATABASE_CONNECTED)} at host: ${chalk.cyan(mongoose.connection.host)}`
    );
  } catch (error) {
    logger.error(chalk.bgRed(`${ValidationMessages.SERVER.DATABASE_ERROR}: ${error.message}`));
    process.exit(1); // Graceful exit on critical DB failure to avoid undefined app states
  }
};

export default dbConnection;
