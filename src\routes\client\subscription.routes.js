import express from 'express';
import {
  getActiveSubscription,
  getSubscriptionHistory,
  getSubscriptionById,
  cancelSubscription,
} from '../../controllers/order/subscription.controller.js';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { UserRoles } from '../../constants/userRoles.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/subscriptions/active
 * @desc    Get user's active subscription
 * @access  Client only
 */
router.get('/active', getActiveSubscription);

/**
 * @route   GET /api/subscriptions/history
 * @desc    Get user's subscription history
 * @access  Client only
 * @query   { page?, limit? }
 */
router.get('/history', getSubscriptionHistory);

/**
 * @route   GET /api/subscriptions/:subscriptionId
 * @desc    Get subscription details by ID
 * @access  Client only
 */
router.get('/:subscriptionId', validateMongoId(), getSubscriptionById);

/**
 * @route   POST /api/subscriptions/cancel
 * @desc    Cancel user's active subscription
 * @access  Client only
 * @body    { reason? }
 */
router.post('/cancel', cancelSubscription);

export default router;
