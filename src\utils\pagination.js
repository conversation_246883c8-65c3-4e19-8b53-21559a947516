import { AppError } from './errorHandler.js';

const paginate = async ({
  model,
  query = {},
  page = 1,
  limit = 10,
  populateFields = '',
  selectFields = '',
  sort = { createdAt: -1 },
}) => {
  try {
    page = Math.max(parseInt(page), 1);
    limit = Math.max(parseInt(limit), 1);
    const skip = (page - 1) * limit;

    const total = await model.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    if (page > totalPages && total > 0) {
      return {
        data: [],
        total,
        page,
        totalPages,
        hasNextPage: false,
        hasPrevPage: page > 1,
      };
    }

    let queryExec = model.find(query).select(selectFields).sort(sort).skip(skip).limit(limit).lean();

    if (populateFields) {
      queryExec = queryExec.populate(populateFields);
    }

    const data = await queryExec;

    return {
      data,
      total,
      page,
      totalPages,
      hasNextPage: page * limit < total,
      hasPrevPage: page > 1,
    };
  } catch (error) {
    throw new AppError(`Pagination Error: ${error.message}`);
  }
};

export default paginate;
