import { ValidationMessages } from '../../constants/messages.js';
import { AppError, ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import planFeatureModel from '../../models/pricing/planFeature.modal.js';
import { planFeaturesSerializer } from '../../serializers/pricingPlanSerializer.js';

class PlanFeatureService {
  static async createPlanFeature(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { name, description } = req.body;

    if (!name) {
      throw new ValidationError('Name and slug are required fields');
    }

    const slug = name.toLowerCase().replace(/ /g, '-');

    const existingFeature = await planFeatureModel.findOne({ slug });
    if (existingFeature) {
      throw new ValidationError('Feature with this slug already exists');
    }

    const newPlanFeature = new planFeatureModel({
      name,
      description,
      slug,
    });

    await newPlanFeature.save();

    return planFeaturesSerializer(newPlanFeature);
  }

  static async getPlanFeatures(req, res) {
    const planFeatures = await planFeatureModel.find({}).sort({ createdAt: -1 }).lean();
    if (!planFeatures) {
      throw new NotFoundError('Plan features not found');
    }

    const formattedFeatures = planFeatures.map((feature) => planFeaturesSerializer(feature));

    return formattedFeatures;
  }

  static async getPlanFeatureById(req, res) {
    const { id } = req.params;

    const planFeature = await planFeatureModel.findById(id);

    if (!planFeature) {
      throw new NotFoundError('Plan feature not found');
    }

    return planFeaturesSerializer(planFeature);
  }

  static async updatePlanFeature(req, res) {
    const { id } = req.params;

    const updates = req.body;

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const planFeature = await planFeatureModel.findById(id);

    if (!planFeature) {
      throw new NotFoundError('Plan feature not found');
    }

    if (updates.slug && updates.slug !== planFeature.slug) {
      const existingFeature = await planFeatureModel.findOne({ slug: updates.slug });
      if (existingFeature) {
        throw new ValidationError('Feature with this slug already exists');
      }
    }

    Object.assign(planFeature, updates);
    await planFeature.save();

    return planFeaturesSerializer(planFeature);
  }

  static async deletePlanFeature(req, res) {
    const { id } = req.params;

    const planFeature = await planFeatureModel.findById(id);

    if (!planFeature) {
      throw new NotFoundError('Plan feature not found');
    }

    await planFeatureModel.findByIdAndDelete(id);

    return null;
  }
}

export default PlanFeatureService;
