import PlanDurationOptionService from '../../services/pricing/planDurationOption.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import successResponse from '../../utils/successResponse.js';

const createPlanDurationOption = asyncHandler(async (req, res) => {
  const planDurationOptionData = await PlanDurationOptionService.createPlanDurationOption(req, res);

  successResponse(
    res,
    HttpStatus.STATUS_CODE.CREATED,
    'Plan duration option created successfully',
    planDurationOptionData
  );
});

const getPlanDurationOptions = asyncHandler(async (req, res) => {
  const planDurationOptions = await PlanDurationOptionService.getPlanDurationOptions(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Plan duration options retrieved successfully', planDurationOptions);
});

const getPlanDurationOptionById = asyncHandler(async (req, res) => {
  const planDurationOption = await PlanDurationOptionService.getPlanDurationOptionById(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Plan duration option retrieved successfully', planDurationOption);
});

const updatePlanDurationOption = asyncHandler(async (req, res) => {
  const updatedPlanDurationOption = await PlanDurationOptionService.updatePlanDurationOption(req, res);
  successResponse(
    res,
    HttpStatus.STATUS_CODE.OK,
    'Plan duration option updated successfully',
    updatedPlanDurationOption
  );
});

const deletePlanDurationOption = asyncHandler(async (req, res) => {
  const deletedPlanDurationOption = await PlanDurationOptionService.deletePlanDurationOption(req, res);

  successResponse(
    res,
    HttpStatus.STATUS_CODE.OK,
    'Plan duration option deleted successfully',
    deletedPlanDurationOption
  );
});

export {
  createPlanDurationOption,
  getPlanDurationOptions,
  getPlanDurationOptionById,
  updatePlanDurationOption,
  deletePlanDurationOption,
};
