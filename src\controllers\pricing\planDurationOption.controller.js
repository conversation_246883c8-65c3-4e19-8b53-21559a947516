import PlanDurationOptionService from '../../services/pricing/planDurationOption.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';

const createPlanDurationOption = asyncHandler(async (req, res) => {
  const planDurationOptionData = await PlanDurationOptionService.createPlanDurationOption(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan duration option created successfully',
    data: planDurationOptionData,
  });
});

const getPlanDurationOptions = asyncHandler(async (req, res) => {
  const planDurationOptions = await PlanDurationOptionService.getPlanDurationOptions(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan duration options retrieved successfully',
    data: planDurationOptions,
  });
});

const getPlanDurationOptionById = asyncHandler(async (req, res) => {
  const planDurationOption = await PlanDurationOptionService.getPlanDurationOptionById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan duration option retrieved successfully',
    data: planDurationOption,
  });
});

const updatePlanDurationOption = asyncHandler(async (req, res) => {
  const updatedPlanDurationOption = await PlanDurationOptionService.updatePlanDurationOption(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan duration option updated successfully',
    data: updatedPlanDurationOption,
  });
});

const deletePlanDurationOption = asyncHandler(async (req, res) => {
  const deletedPlanDurationOption = await PlanDurationOptionService.deletePlanDurationOption(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Plan duration option deleted successfully',
    data: deletedPlanDurationOption,
  });
});

export {
  createPlanDurationOption,
  getPlanDurationOptions,
  getPlanDurationOptionById,
  updatePlanDurationOption,
  deletePlanDurationOption,
};
