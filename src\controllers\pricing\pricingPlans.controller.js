import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import PricingPlanService from '../../services/pricing/pricingPlans.sevice.js';

const createPricingPlan = asyncHandler(async (req, res) => {
  const pricingPlanData = await PricingPlanService.createPricingPlan(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Pricing plan created successfully',
    data: pricingPlanData,
  });
});

const getPricingPlans = asyncHandler(async (req, res) => {
  const pricingPlans = await PricingPlanService.getPricingPlans(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Pricing plans retrieved successfully',
    data: pricingPlans,
  });
});

const getPricingPlanById = asyncHandler(async (req, res) => {
  const pricingPlan = await PricingPlanService.getPricingPlanById(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Pricing plan retrieved successfully',
    data: pricingPlan,
  });
});

const updatePricingPlan = asyncHandler(async (req, res) => {
  const updatedPricingPlan = await PricingPlanService.updatePricingPlan(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Pricing plan updated successfully',
    data: updatedPricingPlan,
  });
});

const deletePricingPlan = asyncHandler(async (req, res) => {
  const deletedPricingPlan = await PricingPlanService.deletePricingPlan(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Pricing plan deleted successfully',
    data: deletedPricingPlan,
  });
});

export { createPricingPlan, getPricingPlans, getPricingPlanById, updatePricingPlan, deletePricingPlan };
