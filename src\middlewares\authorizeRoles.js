import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';
import { AppError, ForbiddenError, UnauthorizedError, ValidationError } from '../utils/errorHandler.js';

/**
 * Middleware to authorize user roles against allowed roles.
 * @param  {...string} allowedRoles - List of roles that are permitted to access the route.
 * @returns {Function} Express middleware function.
 */
const authorizeRoles = (...allowedRoles) => {
  return (req, res, next) => {
    try {
      const { user } = req;

      if (!user) {
        throw new UnauthorizedError(ValidationMessages.AUTHENTICATION.SESSION_EXPIRED);
      }

      const userRoles = Array.isArray(user.roles) ? user.roles : [user.roles];

      if (userRoles.length === 0) {
        throw new ForbiddenError(`Authorization failed: No roles assigned. User ID: ${user._id}`);
      }

      const isAuthorized = allowedRoles.some((role) => userRoles.includes(role));
      if (!isAuthorized) {
        throw new ForbiddenError(
          `Authorization failed: User roles [${userRoles}] do not match required roles [${allowedRoles}].`
        );
      }

      next();
    } catch (error) {
      next(error); // Preserve the original error context
    }
  };
};

export default authorizeRoles;
