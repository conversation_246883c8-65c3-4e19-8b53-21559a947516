import mongoose from 'mongoose';
import { SubscriptionStatus } from '../../constants/enums.js';
import { AppError } from '../../utils/errorHandler.js';

const subscriptionSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PricingPlan',
      required: true,
    },
    durationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PlanDurationOption',
      required: true,
    },
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PlanPurchase',
      required: true,
    },
    status: {
      type: String,
      enum: SubscriptionStatus.ALL,
      default: SubscriptionStatus.PENDING,
      required: true,
    },
    startDate: {
      type: Date,
      default: null,
    },
    endDate: {
      type: Date,
      default: null,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    features: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Feature',
      },
    ],
    metadata: {
      type: Object,
      default: null,
    },
  },
  { timestamps: true }
);

// Indexes for optimized querying
subscriptionSchema.index({ userId: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ isActive: 1 });
subscriptionSchema.index({ endDate: 1 });
subscriptionSchema.index({ userId: 1, isActive: 1 });

// Virtual to check if subscription is expired
subscriptionSchema.virtual('isExpired').get(function () {
  return this.endDate && new Date() > this.endDate;
});

// Method to activate subscription
subscriptionSchema.methods.activate = function () {
  this.status = SubscriptionStatus.ACTIVE;
  this.isActive = true;
  this.startDate = new Date();
  return this.save();
};

// Method to cancel subscription
subscriptionSchema.methods.cancel = function () {
  this.status = SubscriptionStatus.CANCELLED;
  this.isActive = false;
  return this.save();
};

// Method to expire subscription
subscriptionSchema.methods.expire = function () {
  this.status = SubscriptionStatus.EXPIRED;
  this.isActive = false;
  return this.save();
};

// Method to renew subscription
subscriptionSchema.methods.renew = async function () {
  const duration = await mongoose.model('PlanDurationOption').findById(this.durationId);
  if (!duration) throw new AppError('Duration info missing for renewal');

  this.status = SubscriptionStatus.ACTIVE;
  this.isActive = true;
  this.startDate = new Date();
  this.endDate = new Date(this.startDate.getTime() + duration.valueInDays * 24 * 60 * 60 * 1000);
  return this.save();
};

export default mongoose.models.Subscription || mongoose.model('Subscription', subscriptionSchema, 'subscriptions');
