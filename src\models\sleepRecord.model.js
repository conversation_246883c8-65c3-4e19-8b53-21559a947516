import mongoose from 'mongoose';
import { SleepQuality } from '../constants/enums.js';

const SleepRecordSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    sleepDuration: {
      type: Number,
      required: true,
      min: 0,
      max: 24,
      validate: {
        validator: function (value) {
          return value >= 0 && value <= 24;
        },
        message: 'Sleep duration must be between 0 and 24 hours',
      },
      default: 0,
    },
    sleepQuality: {
      type: String,
      enum: SleepQuality.ALL,
      default: SleepQuality.GOOD,
      required: true,
    },
    sleepDate: {
      type: Date,

      default: null,
    },
    sleepTime: {
      type: Date,
      default: null,
    },
    awakeTime: {
      type: Date,
      default: null,
    },
    notes: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Ensure 1 record per user per sleepDate
SleepRecordSchema.index({ userId: 1, sleepDate: 1 }, { unique: true });

// Auto-calculate sleepDuration before save
SleepRecordSchema.pre('save', function (next) {
  if (this.sleepTime && this.awakeTime) {
    const duration = (this.awakeTime - this.sleepTime) / (1000 * 60 * 60);
    this.sleepDuration = Math.max(0, +duration.toFixed(2)); // Clamp to 0, round to 2 decimals
  }
  next();
});

export default mongoose.models.SleepRecord || mongoose.model('SleepRecord', SleepRecordSchema, 'sleep_records');
