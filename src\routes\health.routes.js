import express from 'express';
import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';
import successResponse from '../utils/successResponse.js';
import logger from '../utils/logger.js';

const router = express.Router();

/**
 * @route   GET /api-health
 * @desc    Basic health check endpoint for simple monitoring
 * @access  Public
 * @returns {Object} Basic server status
 */
router.get('/api-health', (req, res) => {
  successResponse(res, HttpStatus.STATUS_CODE.OK, ValidationMessages.SERVER.HEALTHY);
});

/**
 * @route   GET /api/health
 * @desc    Comprehensive health check with detailed system metrics
 * @access  Public
 * @returns {Object} Detailed server health information including uptime, memory, and services
 */
router.get('/api/health', async (req, res) => {
  try {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.version,
      memory: {
        used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
        total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
        external: Math.round((process.memoryUsage().external / 1024 / 1024) * 100) / 100,
      },
      services: {
        database: 'connected',
        server: 'running',
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
      },
    };

    successResponse(res, HttpStatus.STATUS_CODE.OK, ValidationMessages.SERVER.HEALTHY, healthData);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(HttpStatus.STATUS_CODE.INTERNAL_SERVER_ERROR).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

/**
 * @route   GET /api/health/detailed
 * @desc    Extended health check with additional diagnostics
 * @access  Public
 * @returns {Object} Extended health information with performance metrics
 */
router.get('/api/health/detailed', async (req, res) => {
  try {
    const startTime = process.hrtime();

    // Simulate some basic checks
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const endTime = process.hrtime(startTime);
    const responseTime = endTime[0] * 1000 + endTime[1] * 1e-6; // Convert to milliseconds

    const detailedHealthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      responseTime: Math.round(responseTime * 100) / 100,
      uptime: {
        seconds: process.uptime(),
        formatted: formatUptime(process.uptime()),
      },
      environment: {
        nodeEnv: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        processId: process.pid,
      },
      memory: {
        heapUsed: Math.round((memoryUsage.heapUsed / 1024 / 1024) * 100) / 100,
        heapTotal: Math.round((memoryUsage.heapTotal / 1024 / 1024) * 100) / 100,
        external: Math.round((memoryUsage.external / 1024 / 1024) * 100) / 100,
        rss: Math.round((memoryUsage.rss / 1024 / 1024) * 100) / 100,
        arrayBuffers: Math.round((memoryUsage.arrayBuffers / 1024 / 1024) * 100) / 100,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      services: {
        database: 'connected',
        server: 'running',
        api: 'operational',
      },
      checks: {
        database: true,
        memory: memoryUsage.heapUsed < memoryUsage.heapTotal * 0.9, // Less than 90% memory usage
        uptime: process.uptime() > 0,
      },
    };

    successResponse(res, HttpStatus.STATUS_CODE.OK, 'Detailed health check completed', detailedHealthData);
  } catch (error) {
    logger.error('Detailed health check failed:', error);
    res.status(HttpStatus.STATUS_CODE.INTERNAL_SERVER_ERROR).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      checks: {
        database: false,
        memory: false,
        uptime: false,
      },
    });
  }
});

/**
 * Helper function to format uptime in human-readable format
 * @param {number} seconds - Uptime in seconds
 * @returns {string} Formatted uptime string
 */
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);

  return parts.join(' ');
}

export default router;
