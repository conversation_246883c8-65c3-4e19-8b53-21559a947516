import express from 'express';
import {
  createPaymentOrder,
  verifyPayment,
  getPaymentHistory,
  getPaymentDetails,
} from '../../controllers/order/payment.controller.js';

import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { UserRoles } from '../../constants/userRoles.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Clients
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   POST /api/payments/create-order
 * @desc    Create a payment order for plan purchase
 * @access  Client only
 * @body    { planId, durationId, couponCode? }
 */
router.post('/create-order', createPaymentOrder);

/**
 * @route   POST /api/payments/verify
 * @desc    Verify payment and activate subscription
 * @access  Client only
 * @body    { razorpay_order_id, razorpay_payment_id, razorpay_signature }
 */
router.post('/verify', verifyPayment);

/**
 * @route   GET /api/payments/history
 * @desc    Get user's payment history
 * @access  Client only
 * @query   { page?, limit? }
 */
router.get('/history', getPaymentHistory);

/**
 * @route   GET /api/payments/:orderId
 * @desc    Get payment details by order ID
 * @access  Client only
 */
router.get('/:orderId', validateMongoId(), getPaymentDetails);

export default router;
