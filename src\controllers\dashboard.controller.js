import asyncHandler from '../utils/asyncHandler.js';
import DashboardOverviewService from '../services/dashboardOverview.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import successResponse from '../utils/successResponse.js';

const getDashboardOverview = asyncHandler(async (req, res) => {
  const dashboardOverview = await DashboardOverviewService.getDashboardOverview(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Dashboard overview retrieved successfully', dashboardOverview);
});

export { getDashboardOverview };
