import asyncHandler from '../utils/asyncHandler.js';
import DashboardOverviewService from '../services/dashboardOverview.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';

const getDashboardOverview = asyncHandler(async (req, res) => {
  const dashboardOverview = await DashboardOverviewService.getDashboardOverview(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: dashboardOverview,
  });
});

export { getDashboardOverview };
