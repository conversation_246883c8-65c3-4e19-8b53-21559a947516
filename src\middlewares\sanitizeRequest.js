/**
 * Recursively trims all string values in an object.
 * This function ensures that any nested objects are also sanitized.
 *
 * @param {Object} obj - The object to sanitize.
 */
const trimStrings = (obj) => {
  Object.entries(obj).forEach(([key, value]) => {
    // If the value is a string, trim leading/trailing whitespace
    if (typeof value === 'string') {
      obj[key] = value.trim();
    }
    // If the value is a non-array object, recurse into it
    else if (value && typeof value === 'object' && !Array.isArray(value)) {
      trimStrings(value);
    }
  });
};

/**
 * Express middleware to sanitize incoming request body.
 * Trims whitespace from all string fields (including nested objects) in req.body.
 *
 * Usage: app.use(sanitizeRequest);
 */
export const sanitizeRequest = (req, res, next) => {
  if (req.body) trimStrings(req.body);
  next();
};
