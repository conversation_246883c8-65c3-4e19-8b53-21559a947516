import mongoose from 'mongoose';

const pricingPlanSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
    },
    description: {
      type: String,
      default: null,
    },
    durations: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PlanDurationOption',
        required: true,
      },
    ],
    isActive: {
      type: Boolean,
      default: true,
    },
    isPopular: {
      type: Boolean,
      default: false,
    },
    customSort: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

export default mongoose.models.PricingPlan || mongoose.model('PricingPlan', pricingPlanSchema, 'pricing_plans');
