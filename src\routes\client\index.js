import express from 'express';

// Import client route modules
import profileRoutes from './profile.routes.js';
import sleepTrackingRoutes from './sleep-tracking.routes.js';
import paymentRoutes from './payment.routes.js';
import subscriptionRoutes from './subscription.routes.js';
import couponRoutes from './coupon.routes.js';

const router = express.Router();

/**
 * ===== CLIENT API ROUTES =====
 * All routes require client authentication and authorization
 */

// Client profile and settings management
router.use('/profile', profileRoutes);

// Health tracking features
router.use('/sleep-tracking', sleepTrackingRoutes);

// Payment and subscription management
router.use('/payments', paymentRoutes);
router.use('/subscriptions', subscriptionRoutes);
router.use('/coupons', couponRoutes);

export default router;
