// ===== CORE SYSTEM ROUTES =====
import authRoutes from './auth.routes.js';
import healthRoutes from './health.routes.js';
import staticRoutes from './static.routes.js';

// ===== PUBLIC ROUTES =====
import publicRoutes from './public/index.js';

// ===== CLIENT ROUTES =====
import clientRoutes from './client/index.js';

// ===== ADMIN ROUTES =====
import adminRoutes from './admin/index.js';

// ===== EXTERNAL INTEGRATION ROUTES =====
// import webhookRoutes from './webhook.routes.js'; // TODO: Create webhook routes

const registerRoutes = (app) => {
  // ===== CORE SYSTEM ROUTES =====
  // Authentication & Authorization
  app.use('/api/auth', authRoutes);

  // Health Check & Monitoring
  app.use('/', healthRoutes);

  // Static Pages & UI Routes
  app.use('/', staticRoutes);

  // ===== PUBLIC API ROUTES =====
  // Public information (no authentication required)
  app.use('/api', publicRoutes);

  // ===== CLIENT API ROUTES =====
  // Client-specific functionality (requires client authentication)
  app.use('/api/clients', clientRoutes);

  // ===== ADMIN API ROUTES =====
  // Admin-specific functionality (requires admin authentication)
  app.use('/api/admin', adminRoutes);

  // ===== EXTERNAL INTEGRATIONS =====
  // Webhook handlers (should be before other middleware)
  // app.use('/api/webhooks', webhookRoutes); // TODO: Create webhook routes
};

export default registerRoutes;
