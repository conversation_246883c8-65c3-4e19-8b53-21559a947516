import authRoutes from './auth.routes.js';
import clientClientsRouter from './client/clients.routes.js';
import clientAdminRoutes from './admin/client.admin.routes.js';
import clientSleepRoutes from './client/client.sleep.routes.js';
import pricingRoutes from './admin/pricing/index.js';
import dashboardOverviewRoutes from './admin/dashboard.overview.routes.js';
import couponAdminRoutes from './admin/coupon.admin.routes.js';
import couponRoutes from './client/coupon.routes.js';
import paymentRoutes from './client/payment.routes.js';
import subscriptionRoutes from './client/subscription.routes.js';
import paymentAdminRoutes from './admin/payment.admin.routes.js';
import webhookRoutes from './webhook.routes.js';
import pricingPlansRoutes from './pricing.routes.js';

const registerRoutes = (app) => {
  // Authentication & Authorization
  app.use('/api/auth', authRoutes);

  // Pricing
  app.use('/api/pricing-plans', pricingPlansRoutes);

  // Client
  app.use('/api/clients', clientClientsRouter);
  app.use('/api/clients/sleep-tracking', clientSleepRoutes);
  app.use('/api/coupons', couponRoutes);
  app.use('/api/payments', paymentRoutes);
  app.use('/api/subscriptions', subscriptionRoutes);

  // Admin
  app.use('/api/admin/clients', clientAdminRoutes);
  app.use('/api/admin/dashboard', dashboardOverviewRoutes);
  app.use('/api/admin/coupons', couponAdminRoutes);
  app.use('/api/admin', paymentAdminRoutes);
  app.use('/api/pricing', pricingRoutes);

  // Webhooks
  app.use('/api/webhooks', webhookRoutes);
};

export default registerRoutes;
