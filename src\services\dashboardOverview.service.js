import userModel from '../models/user.model.js';
import planPurchaseModel from '../models/order/order.modal.js';
import dayjs from 'dayjs';

class DashboardOverviewService {
  static async getDashboardOverview(req, res) {
    const now = dayjs();
    const oneMonthAgo = now.subtract(1, 'month').toDate();
    const oneHourAgo = now.subtract(1, 'hour').toDate();

    const totalUsers = await userModel.countDocuments();
    const newSignups = await userModel.countDocuments({ createdAt: { $gte: oneMonthAgo } });
    const totalActiveUsers = await userModel.countDocuments({ isDeactivated: false });

    // Placeholder for "active xyz" - this should be replaced by real logic
    const activeXyz = await userModel.countDocuments({ updatedAt: { $gte: oneHourAgo } });

    const totalPlanPurchases = await planPurchaseModel.countDocuments();

    const totalRevenueAgg = await planPurchaseModel.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);

    const totalRevenue = totalRevenueAgg.length > 0 ? totalRevenueAgg[0].total : 0;

    return {
      totalUsers,
      newSignups,
      totalActiveUsers,
      activeXyz,
      totalPlanPurchases,
      totalRevenue,
    };
  }
}

export default DashboardOverviewService;
