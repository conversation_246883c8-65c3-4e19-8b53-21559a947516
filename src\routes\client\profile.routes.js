import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';

import { getClientProfile, onboardingProcess, updateClientDetails } from '../../controllers/clients.controller.js';
import { getUserSettings, updateUserSettings } from '../../controllers/clientsSettings.controller.js';

const router = express.Router();

// Apply authentication and authorization middleware to all client profile routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   POST /api/clients/profile/onboarding
 * @desc    Submit onboarding data during first-time setup
 * @access  Client only
 */
router.post('/onboarding', onboardingProcess);

/**
 * @route   GET /api/clients/profile/me
 * @desc    Retrieve authenticated client's full profile
 * @access  Client only
 */
router.get('/me', getClientProfile);

/**
 * @route   PUT /api/clients/profile/me
 * @desc    Update authenticated client's profile information
 * @access  Client only
 */
router.put('/me', updateClientDetails);

/**
 * @route   GET /api/clients/profile/settings
 * @desc    Get client-specific user settings (preferences, flags, etc.)
 * @access  Client only
 */
router.get('/settings', getUserSettings);

/**
 * @route   PATCH /api/clients/profile/settings
 * @desc    Update client-specific user settings (partial update)
 * @access  Client only
 */
router.patch('/settings', updateUserSettings);

export default router;
