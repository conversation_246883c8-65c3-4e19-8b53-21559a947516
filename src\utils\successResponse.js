import { HttpStatus } from '../constants/httpStatus.js';

/**
 * Utility function to standardize successful HTTP responses
 *
 * @param {Response} res - Express response object
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Success message
 * @param {*} data - Data to return to the client
 */
const sendResponse = (res, statusCode, message, data) => {
  res.status(statusCode).json({
    status: HttpStatus.STATUS.SUCCESS,
    message,
    data,
  });
};

export default sendResponse;
