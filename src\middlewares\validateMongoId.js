import mongoose from 'mongoose';
import { ValidationError } from '../utils/errorHandler.js';

/**
 * Middleware to validate MongoDB ObjectId parameters in request URLs.
 *
 * @param {string} param - The name of the parameter in req.params to validate (default: 'id').
 * @returns {function} Express middleware function.
 */
export const validateMongoId =
  (param = 'id') =>
  (req, res, next) => {
    const id = req.params[param];

    if (!mongoose.Types.ObjectId.isValid(id)) {
      // Pass validation error to the error-handling middleware
      return next(new ValidationError(`Invalid Mongo ID format: ${id}`));
    }

    // Proceed to next middleware or route handler if valid
    next();
  };
