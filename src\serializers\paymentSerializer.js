import config from '../config/environment.config.js';

/**
 * Payment Order Serializer
 * Formats payment order data for API responses
 */
export const paymentOrderSerializer = (orderDoc) => {
  const {
    _id,
    userId,
    planId,
    durationId,
    razorpay_order_id,
    razorpay_payment_id,
    razorpay_signature,
    amount,
    currency,
    paymentType,
    status,
    paymentDate,
    createdAt,
    updatedAt,
  } = orderDoc;

  return {
    id: _id,
    userId: userId,
    planId: planId,
    durationId: durationId,
    razorpayOrderId: razorpay_order_id,
    razorpayPaymentId: razorpay_payment_id,
    razorpaySignature: razorpay_signature,
    amount: amount,
    currency: currency,
    paymentType: paymentType,
    status: status,
    paymentDate: paymentDate,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

/**
 * Payment Order with Plan Details Serializer
 * Formats payment order data with populated plan information
 */
export const paymentOrderWithPlanSerializer = (orderDoc) => {
  const {
    _id,
    userId,
    planId,
    durationId,
    razorpay_order_id,
    razorpay_payment_id,
    razorpay_signature,
    amount,
    currency,
    paymentType,
    status,
    paymentDate,
    createdAt,
    updatedAt,
  } = orderDoc;

  return {
    id: _id,
    userId: userId,
    plan: planId
      ? {
          id: planId._id,
          name: planId.name,
          slug: planId.slug,
          description: planId.description,
        }
      : null,
    duration: durationId
      ? {
          id: durationId._id,
          label: durationId.label,
          valueInDays: durationId.valueInDays,
          price: durationId.price,
          currency: durationId.currency,
        }
      : null,
    razorpayOrderId: razorpay_order_id,
    razorpayPaymentId: razorpay_payment_id,
    razorpaySignature: razorpay_signature,
    amount: amount,
    currency: currency,
    paymentType: paymentType,
    status: status,
    paymentDate: paymentDate,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

/**
 * Razorpay Order Creation Response Serializer
 * Formats Razorpay order creation response for frontend
 */
export const razorpayOrderSerializer = (razorpayOrder, orderDoc) => {
  return {
    orderId: orderDoc._id,
    razorpayOrderId: razorpayOrder.id,
    amount: razorpayOrder.amount,
    currency: razorpayOrder.currency,
    receipt: razorpayOrder.receipt,
    status: razorpayOrder.status,
    createdAt: razorpayOrder.created_at,
    key: config.razorpay_key_id,
    originalAmount: orderDoc.metadata?.originalAmount,
    discountedAmount: orderDoc.amount,
    appliedCoupon: orderDoc.metadata?.appliedCoupon,
    planDetails: {
      planId: orderDoc.planId,
      durationId: orderDoc.durationId,
    },
  };
};

/**
 * Payment Success Response Serializer
 * Formats successful payment response
 */
export const paymentSuccessSerializer = (orderDoc, subscriptionDoc = null) => {
  return {
    order: paymentOrderSerializer(orderDoc),
    subscription: subscriptionDoc
      ? {
          id: subscriptionDoc._id,
          status: subscriptionDoc.status,
          startDate: subscriptionDoc.startDate,
          endDate: subscriptionDoc.endDate,
          isActive: subscriptionDoc.isActive,
        }
      : null,
  };
};
