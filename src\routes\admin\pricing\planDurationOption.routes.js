import express from 'express';
import validateAccessToken from '../../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../../constants/userRoles.js';
import { validateMongoId } from '../../../middlewares/validateMongoId.js';

import {
  createPlanDurationOption,
  getPlanDurationOptions,
  getPlanDurationOptionById,
  updatePlanDurationOption,
  deletePlanDurationOption,
} from '../../../controllers/pricing/planDurationOption.controller.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/pricing/durations
 * @desc    Create a new plan duration option (e.g., 1 month, 6 months)
 * @access  Admin only
 */
router.post('/', createPlanDurationOption);

/**
 * @route   GET /api/pricing/durations
 * @desc    Fetch all available plan duration options
 * @access  Admin only
 */
router.get('/', getPlanDurationOptions);

/**
 * @route   GET /api/pricing/durations/:id
 * @desc    Get a specific plan duration option by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getPlanDurationOptionById);

/**
 * @route   PUT /api/pricing/durations/:id
 * @desc    Update a specific plan duration option
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updatePlanDurationOption);

/**
 * @route   DELETE /api/pricing/durations/:id
 * @desc    Delete a specific plan duration option
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deletePlanDurationOption);

export default router;
