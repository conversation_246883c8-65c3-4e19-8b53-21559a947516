import nodemailer from 'nodemailer';
import config from './environment.config.js';

const transporter = nodemailer.createTransport({
  host: config.smtp_host,
  port: Number(config.smtp_port), // ensure port is a number
  secure: config.smtp_port == 465, // use SSL for port 465, else false
  requireTLS: true, // enforce TLS for security

  tls: {
    rejectUnauthorized: false, // Allows self-signed certificates; consider enabling true in production
  },

  auth: {
    user: config.smtp_user,
    pass: config.smtp_pass,
  },
});

export default transporter;
