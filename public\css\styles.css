/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-size: 16px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: #667eea;
}

.nav-brand i {
  font-size: 1.5rem;
  color: #e74c3c;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #667eea;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  align-items: center;
}

.hero-content {
  text-align: left;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  line-height: 1.1;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0.95;
  line-height: 1.7;
  max-width: 600px;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.stat {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #fff;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.95rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  font-weight: 500;
}

/* Enhanced Hero Visual Styles */
.system-status-preview {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  font-weight: 700;
  font-size: 1.2rem;
  color: #fff;
}

.status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #10b981;
  animation: pulse 2s infinite;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

.status-dot.offline {
  background: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
}

.status-dot.checking {
  background: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.5);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.metrics-preview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 1rem;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.metric-preview i {
  color: #10b981;
  width: 20px;
  font-size: 1.2rem;
}

/* Overview Section */
.overview {
  padding: 100px 0;
  background: #f8fafc;
}

.section-title {
  text-align: center;
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 4rem;
  color: #1a1a1a;
  line-height: 1.2;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
}

.overview-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.overview-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.overview-card h3 {
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1.2rem;
  color: #1a1a1a;
}

.overview-card p {
  color: #6b7280;
  line-height: 1.7;
  font-size: 1rem;
}

/* Overview Card Enhancements */
.card-metrics {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.metric-badge {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Health Section */
.health {
  padding: 100px 0;
  background: white;
}

/* Health Results */
.health-result {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
}

.health-success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.error-result {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.response-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #059669;
}

.metric-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid #d1fae5;
  font-size: 0.9rem;
}

.metric-item:last-child {
  border-bottom: none;
}

/* Status Classes */
.status-online {
  color: #10b981 !important;
}

.status-offline {
  color: #ef4444 !important;
}

.status-checking {
  color: #f59e0b !important;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.health-status.checking i {
  animation: spin 1s linear infinite;
}

/* Primary Health Grid */
.primary-health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
  margin-bottom: 5rem;
}

.primary-health-card {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.primary-health-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.primary-health-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.12);
}

.health-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.health-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.health-info h3 {
  margin: 0 0 1rem 0;
  color: #1a1a1a;
  font-size: 1.6rem;
  font-weight: 700;
}

.health-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  font-size: 1.2rem;
}

.health-status.online {
  color: #10b981;
}

.health-status.offline {
  color: #ef4444;
}

.health-status.checking {
  color: #f59e0b;
}

.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.metric-row .metric-label {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 600;
}

.metric-row .metric-value {
  color: #1a1a1a;
  font-weight: 700;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 1.1rem;
}

/* Detailed Health Section */
.detailed-health-section {
  margin-bottom: 4rem;
}

.detailed-health-section h3 {
  text-align: center;
  margin-bottom: 3rem;
  color: #1a1a1a;
  font-size: 2rem;
  font-weight: 700;
}

.health-endpoints-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2rem;
}

.endpoint-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.endpoint-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #10b981, #059669);
}

.endpoint-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.endpoint-header {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  margin-bottom: 1.5rem;
}

.endpoint-method {
  background: #10b981;
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.endpoint-path {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 1rem;
  color: #1a1a1a;
  font-weight: 600;
  flex: 1;
}

.endpoint-description {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Live Metrics Dashboard */
.live-metrics-dashboard {
  background: white;
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  position: relative;
  overflow: hidden;
}

.live-metrics-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.live-metrics-dashboard h3 {
  text-align: center;
  margin-bottom: 3rem;
  color: #1a1a1a;
  font-size: 2rem;
  font-weight: 700;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.metric-card {
  background: #f8fafc;
  border-radius: 16px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.metric-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.metric-content .metric-label {
  color: #6b7280;
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.metric-content .metric-value {
  color: #1a1a1a;
  font-size: 1.4rem;
  font-weight: 700;
  font-family: 'Monaco', 'Menlo', monospace;
}

.auto-refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  color: #6b7280;
  font-size: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  font-weight: 500;
}

.auto-refresh-indicator i {
  color: #10b981;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    max-width: 1000px;
    padding: 0 20px;
  }

  .hero-container {
    max-width: 1000px;
    gap: 3rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .primary-health-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
  }
}

@media (max-width: 968px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-content {
    text-align: center;
  }

  .hero-description {
    max-width: 100%;
  }

  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .primary-health-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .health-endpoints-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 640px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .primary-health-card {
    padding: 2rem;
  }

  .health-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .health-info h3 {
    font-size: 1.4rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .metric-card {
    padding: 1.5rem;
  }

  .navbar {
    padding: 1rem 0;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-link {
    font-size: 0.9rem;
  }
}

/* Footer */
.footer {
  background: #1a1a1a;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
}

.footer-brand i {
  color: #e74c3c;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #374151;
  color: #9ca3af;
}

/* Additional Mobile Styles */
@media (max-width: 480px) {
  .footer-content {
    flex-direction: column;
    gap: 1rem;
  }

  .footer-links {
    gap: 1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .primary-health-card {
    padding: 1.5rem;
  }

  .live-metrics-dashboard {
    padding: 2rem;
  }
}
