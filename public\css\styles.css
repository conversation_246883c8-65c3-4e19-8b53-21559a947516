/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: #1a1a1a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: #667eea;
}

.nav-brand i {
  font-size: 1.5rem;
  color: #e74c3c;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #667eea;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.gradient-text {
  background: linear-gradient(45deg, #fff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-visual {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: flex-end;
}

.api-card-preview {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-family: 'Monaco', 'Menlo', monospace;
  animation: float 3s ease-in-out infinite;
}

.api-card-preview:nth-child(2) {
  animation-delay: 1s;
}

.api-card-preview:nth-child(3) {
  animation-delay: 2s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.api-method {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.api-method.get {
  background: #10b981;
  color: white;
}
.api-method.post {
  background: #3b82f6;
  color: white;
}
.api-method.put {
  background: #f59e0b;
  color: white;
}

/* Overview Section */
.overview {
  padding: 80px 0;
  background: white;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: #1a1a1a;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 1.5rem;
}

.overview-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1a1a1a;
}

.overview-card p {
  color: #6b7280;
  line-height: 1.6;
}

/* APIs Section */
.apis {
  padding: 80px 0;
  background: #f8fafc;
}

.api-categories {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.api-category {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.category-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-header i {
  font-size: 1.5rem;
}

.category-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  flex: 1;
}

.endpoint-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.api-endpoints {
  padding: 0;
}

.api-endpoint {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 1rem;
  padding: 1rem 2rem;
  border-bottom: 1px solid #f1f5f9;
  align-items: center;
  transition: background-color 0.2s ease;
}

.api-endpoint:hover {
  background: #f8fafc;
}

.api-endpoint:last-child {
  border-bottom: none;
}

.endpoint-method {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  text-align: center;
}

.endpoint-method.get {
  background: #dcfce7;
  color: #166534;
}
.endpoint-method.post {
  background: #dbeafe;
  color: #1e40af;
}
.endpoint-method.put {
  background: #fef3c7;
  color: #92400e;
}
.endpoint-method.patch {
  background: #f3e8ff;
  color: #7c2d12;
}
.endpoint-method.delete {
  background: #fee2e2;
  color: #991b1b;
}

.endpoint-path {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  color: #374151;
  font-weight: 500;
}

.endpoint-desc {
  color: #6b7280;
  font-size: 0.9rem;
}

.endpoint-access {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.endpoint-access.public {
  background: #dcfce7;
  color: #166534;
}
.endpoint-access.protected {
  background: #fef3c7;
  color: #92400e;
}
.endpoint-access.client {
  background: #dbeafe;
  color: #1e40af;
}
.endpoint-access.admin {
  background: #fee2e2;
  color: #991b1b;
}

/* Health Section */
.health {
  padding: 80px 0;
  background: white;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.health-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.health-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.health-status.online {
  color: #10b981;
}

.health-status.offline {
  color: #ef4444;
}

.health-status i {
  font-size: 0.75rem;
}

.health-check-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
  margin-top: 1rem;
}

.health-check-btn:hover {
  transform: translateY(-2px);
}

/* Enhanced Health Monitoring Styles */
.endpoint-details {
  padding: 1.5rem 2rem;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
}

.endpoint-details h4 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.endpoint-details pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.endpoint-details code {
  font-family: 'Monaco', 'Menlo', monospace;
}

.endpoint-details p {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.6;
}

.health-result {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

.health-success {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.error-result {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.response-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #059669;
}

.metric-item {
  padding: 0.25rem 0;
  border-bottom: 1px solid #d1fae5;
  font-size: 0.8rem;
}

.metric-item:last-child {
  border-bottom: none;
}

/* Status Dashboard */
.status-dashboard {
  margin-top: 3rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.status-dashboard h3 {
  text-align: center;
  margin-bottom: 2rem;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.status-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e5e7eb;
}

.metric-label {
  display: block;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.metric-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #374151;
  font-family: 'Monaco', 'Menlo', monospace;
}

.status-online {
  color: #10b981 !important;
}

.status-offline {
  color: #ef4444 !important;
}

.refresh-btn {
  display: block;
  margin: 0 auto;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn i {
  margin-right: 0.5rem;
}

/* Enhanced Health Grid */
.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.health-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.health-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.health-status.checking {
  color: #f59e0b;
}

.health-status.checking i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Footer */
.footer {
  background: #1a1a1a;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
}

.footer-brand i {
  color: #e74c3c;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #374151;
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .nav-links {
    display: none;
  }

  .api-endpoint {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: left;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
  }
}
