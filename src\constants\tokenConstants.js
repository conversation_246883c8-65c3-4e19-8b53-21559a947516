/**
 * TokenConstants class encapsulates static constants used throughout
 * the authentication and authorization workflows.
 * This includes token types, storage keys, and authentication flags.
 * Freezing the class ensures immutability and prevents accidental changes.
 */
export class TokenConstants {
  /** Authorization scheme prefix commonly used in HTTP headers */
  static BEARER = 'Bearer';

  /** Identifier for JSON Web Token (JWT) */
  static JWT = 'jwt';

  /** Key name used to store or retrieve the access token (short-lived token) */
  static ACCESS_TOKEN = 'accessToken';

  /** Flag indicating if the user is authenticated */
  static IS_AUTH = 'is_auth';

  /** Key name used to store or retrieve the refresh token (long-lived token) */
  static REFRESH_TOKEN = 'refreshToken';

  /** Security mode or policy identifier, e.g., for cookie or token handling */
  static STRICT = 'strict';
}

/**
 * Object.freeze ensures that the TokenConstants class and its properties
 * cannot be modified at runtime, enhancing security and predictability.
 */
Object.freeze(TokenConstants);
