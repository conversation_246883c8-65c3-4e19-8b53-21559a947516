import mongoose from 'mongoose';
import { UserRoles } from '../constants/userRoles.js';
import { ValidationMessages } from '../constants/messages.js';

const UserSchema = new mongoose.Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, ValidationMessages.AUTHENTICATION.INVALID_EMAIL_FORMAT],
      trim: true,
    },
    password: {
      type: String,
      trim: true,
      required: true,
    },
    firstName: {
      type: String,
      default: null,
    },
    lastName: {
      type: String,
      default: null,
    },
    phoneNumber: {
      type: String,
      default: null,
    },
    dateOfBirth: {
      type: Date,
      default: null,
    },
    roles: {
      type: String,
      enum: Object.values(UserRoles.ALL),
      required: true,
      default: UserRoles.CLIENT,
    },
    profile: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'roleRef',
    },
    roleRef: {
      type: String,
      enum: UserRoles.ALL,
      required: true,
      default: UserRoles.CLIENT,
    },
    avatar: {
      type: String,
      default: null,
    },
    isVerified: {
      type: Boolean,
      required: true,
      default: false,
    },
    isOnboarded: {
      type: Boolean,
      default: false,
    },
    isDeactivated: {
      type: Boolean,
      required: true,
      default: false,
    },
    deactivationReason: {
      type: String,
      default: null,
    },
    deactivatedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Indexes for optimized querying
UserSchema.index({ profile: 1 });
UserSchema.index({ roles: 1 });
UserSchema.index({ isOnboarded: 1 });
UserSchema.index({ isVerified: 1 });
UserSchema.index({ isDeactivated: 1 });
UserSchema.index({ plan: 1 });
UserSchema.index({ payment: 1 });

export default mongoose.models.User || mongoose.model('User', UserSchema, 'users');
