import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';

const rateLimiter = (limit, windowMs) => {
  // In-memory storage for rate limiting data
  const requests = new Map();

  return (req, res, next) => {
    const ip = req.ip; // Get the IP address from the request
    const now = Date.now();
    const key = `${ip}:${req.originalUrl}`; // Create a unique key for each IP and route

    if (!requests.has(key)) {
      requests.set(key, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }

    // Get the rate limit data for the key
    const requestData = requests.get(key);

    // If the window has expired, reset the count and set a new reset time
    if (now > requestData.resetTime) {
      requestData.count = 1;
      requestData.resetTime = now + windowMs;
      return next();
    }

    // If the limit is exceeded, send a "Too Many Requests" error
    if (requestData.count >= limit) {
      return res.status(HttpStatus.STATUS_CODE.TOO_MANY_REQUEST).json({
        status: HttpStatus.STATUS.ERROR,
        message: ValidationMessages.GENERAL.TOO_MANY_REQUEST,
        resetTime: requestData.resetTime,
      });
    }

    // If the count is below the limit, increment the count and proceed
    requestData.count++;

    requests.set(key, requestData);
    next(); // Allow the request to proceed
  };
};

export default rateLimiter;
