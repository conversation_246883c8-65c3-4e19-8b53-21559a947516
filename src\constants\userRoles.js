/**
 * UserRoles class defines the distinct user roles within the system.
 * These roles form the foundation for implementing role-based authorization
 * across the application, ensuring secure and context-aware access control.
 */
export class UserRoles {
  /** Administrator role with the highest level of permissions */
  static ADMIN = 'Admin';

  /** Coach role responsible for managing client fitness/training plans */
  static COACH = 'Coach';

  /** Client role representing end-users consuming the services */
  static CLIENT = 'Client';

  /**
   * Immutable array of all user roles.
   * Useful for validation, enumeration, and role-checking logic.
   */
  static ALL = Object.freeze([UserRoles.ADMIN, UserRoles.COACH, UserRoles.CLIENT]);
}

/**
 * Object.freeze ensures the UserRoles object is immutable at runtime,
 * preventing accidental modifications that could compromise role integrity.
 */
Object.freeze(UserRoles);
