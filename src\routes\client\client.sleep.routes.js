import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import {
  getSleepRecordById,
  getSleepRecords,
  getSleepRecordsRange,
  updateSleepRecord,
} from '../../controllers/sleepTracking.controller.js';

const router = express.Router();

// Global middleware stack: JWT validation for all routes
router.use(validateAccessToken, authenticateJWT);

/**
 * @route   GET /api/sleep
 * @desc    Get all sleep records of the authenticated client
 * @access  Client only
 */
router.get('/', authorizeRoles(UserRoles.CLIENT), getSleepRecords);

/**
 * @route   GET /api/sleep/range
 * @desc    Get sleep records within a date range (query params expected: from, to)
 * @access  Client only
 */
router.get('/range', authorizeRoles(UserRoles.CLIENT), getSleepRecordsRange);

/**
 * @route   PATCH /api/sleep
 * @desc    Update a sleep record for the authenticated client (must include record ID in body)
 * @access  Client only
 */
router.patch('/', authorizeRoles(UserRoles.CLIENT), updateSleepRecord);

/**
 * @route   GET /api/sleep/:id
 * @desc    Fetch a specific sleep record by ID (Admin view of any user's record)
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), authorizeRoles(UserRoles.ADMIN), getSleepRecordById);

export default router;
