import asyncHandler from '../utils/asyncHandler.js';
import UserSettingsService from '../services/clientsSettings.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import successResponse from '../utils/successResponse.js';

const getUserSettings = asyncHandler(async (req, res) => {
  const userSettings = await UserSettingsService.getUserSettings(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'User settings retrieved successfully', userSettings);
});

const updateUserSettings = asyncHandler(async (req, res) => {
  const updatedUserSettings = await UserSettingsService.updateUserSettings(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'User settings updated successfully', updatedUserSettings);
});

export { getUserSettings, updateUserSettings };
