import asyncHandler from '../utils/asyncHandler.js';
import UserSettingsService from '../services/clientsSettings.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';

const getUserSettings = asyncHandler(async (req, res) => {
  const userSettings = await UserSettingsService.getUserSettings(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: userSettings,
  });
});

const updateUserSettings = asyncHandler(async (req, res) => {
  const updatedUserSettings = await UserSettingsService.updateUserSettings(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.GENERAL.SUCCESS,
    data: updatedUserSettings,
  });
});

export { getUserSettings, updateUserSettings };
