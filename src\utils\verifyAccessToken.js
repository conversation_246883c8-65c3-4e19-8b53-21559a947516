import jwt from 'jsonwebtoken';
import { HttpStatus } from '../constants/httpStatus.js';
import config from '../config/environment.config.js';
import { AppError } from './errorHandler.js';

/**
 * Verifies the JWT and checks for expiration.
 * @param {string} token - JWT access token.
 * @returns {object} - Decoded JWT payload if valid and not expired.
 * @throws {AppError} - Throws if the token is invalid or expired.
 */
const verifyAccessToken = (token) => {
  try {
    const decoded = jwt.verify(token, config.jwt_access_token_secret_key, {
      algorithms: [config.jwt_algorithm],
    });

    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp < now) {
      throw new AppError('Token has expired.', HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    return decoded;
  } catch (error) {
    // If it's a JWT error, map to clear messaging
    if (error.name === 'TokenExpiredError') {
      throw new AppError('Token has expired.', HttpStatus.STATUS_CODE.UNAUTHORIZED);
    } else if (error.name === 'JsonWebTokenError') {
      throw new AppError('Invalid token.', HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    // Unknown error (likely from config or secret issues)
    throw new AppError('Token verification failed.', HttpStatus.STATUS_CODE.UNAUTHORIZED);
  }
};

export default verifyAccessToken;
