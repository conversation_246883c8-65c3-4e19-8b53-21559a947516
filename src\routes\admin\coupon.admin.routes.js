import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';

import {
  createCoupon,
  getCoupons,
  getCouponById,
  updateCoupon,
  deleteCoupon,
  activateCoupon,
  deactivateCoupon,
} from '../../controllers/order/coupon.controller.js';

const router = express.Router();

// Global middleware for all routes: Require valid access token and JWT
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/coupons
 * @desc    Create a new coupon
 * @access  Admin only
 */
router.post('/', createCoupon);

/**
 * @route   GET /api/coupons
 * @desc    Get all coupons
 * @access  Admin only
 */
router.get('/', getCoupons);

/**
 * @route   GET /api/coupons/:id
 * @desc    Get a specific coupon by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getCouponById);

/**
 * @route   PUT /api/coupons/:id
 * @desc    Update a specific coupon by ID
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updateCoupon);

/**
 * @route   PATCH /api/coupons/:id/activate
 * @desc    Activate a specific coupon by ID
 * @access  Admin only
 */
router.patch('/:id/activate', validateMongoId(), activateCoupon);

/**
 * @route   PATCH /api/coupons/:id/deactivate
 * @desc    Deactivate a specific coupon by ID
 * @access  Admin only
 */
router.patch('/:id/deactivate', validateMongoId(), deactivateCoupon);

/**
 * @route   DELETE /api/coupons/:id
 * @desc    Delete a specific coupon by ID
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deleteCoupon);

export default router;
