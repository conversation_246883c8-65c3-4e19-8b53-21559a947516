import mongoose from 'mongoose';

const userSettingsSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
    },
    // Sleep
    dailySleepGoalHours: {
      type: Number,
      default: 8,
    },
    sleepReminderTime: {
      type: Date,
      default: null,
    },
    isSleepReminderOn: {
      type: Boolean,
      default: false,
    },
    awakeReminderTime: {
      type: Date,
      default: null,
    },
    isAwakeReminderOn: {
      type: Boolean,
      default: false,
    },
    // Water
    dailyWaterGoalLiters: {
      type: Number,
      default: 2,
    },
    waterReminderTime: {
      type: Date,
      default: null,
    },
    isWaterReminderOn: {
      type: Boolean,
      default: false,
    },
    // Steps
    dailyStepsGoal: {
      type: Number,
      default: 10000,
    },
    stepsReminderTime: {
      type: Date,
      default: null,
    },
    isStepsReminderOn: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

export default mongoose.models.UserSettings || mongoose.model('UserSettings', userSettingsSchema, 'user_settings');
