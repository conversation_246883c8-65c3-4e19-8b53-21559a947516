import mongoose from 'mongoose';

// Defining Schema
const EmailVerificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  otp: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: '15m',
  },
});

EmailVerificationSchema.index({ userId: 1 });

export default mongoose.model('EmailVerification', EmailVerificationSchema, 'email_verifications');
