import express from 'express';
import validateAccessToken from '../../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../../constants/userRoles.js';
import { validateMongoId } from '../../../middlewares/validateMongoId.js';
import {
  createPlanFeature,
  getPlanFeatures,
  getPlanFeatureById,
  updatePlanFeature,
  deletePlanFeature,
} from '../../../controllers/pricing/planFeature.controller.js';

const router = express.Router();

// Global middlewares: Validate access token, authenticate user, and authorize Admin role
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/pricing/features
 * @desc    Create a new plan feature (e.g., number of devices, premium support)
 * @access  Admin only
 */
router.post('/', createPlanFeature);

/**
 * @route   GET /api/pricing/features
 * @desc    Retrieve all plan features
 * @access  Admin only
 */
router.get('/', getPlanFeatures);

/**
 * @route   GET /api/pricing/features/:id
 * @desc    Get a specific plan feature by its MongoDB ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getPlanFeatureById);

/**
 * @route   PUT /api/pricing/features/:id
 * @desc    Update details of a plan feature by ID
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updatePlanFeature);

/**
 * @route   DELETE /api/pricing/features/:id
 * @desc    Delete a plan feature by ID
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deletePlanFeature);

export default router;
