import express from 'express';
import {
  register,
  login,
  forgotPassword,
  resetPassword,
  changePassword,
  logout,
  refreshToken,
  verifyEmail,
  adminLogin,
  sendVerificationEmail,
} from '../controllers/auth.controller.js';
import validateAccessToken from '../middlewares/validateAccessToken.js';
import authenticateJ<PERSON><PERSON> from '../middlewares/authenticateJWT.js';

const router = express.Router();

/**
 * @route   POST /signup
 * @desc    Register a new user (client or admin registration process)
 * @access  Public
 */
router.post('/signup', register);

/**
 * @route   POST /login
 * @desc    User login for standard users (clients)
 * @access  Public
 */
router.post('/login', login);

/**
 * @route   POST /login/admin
 * @desc    Admin user login endpoint with elevated privileges
 * @access  Public
 */
router.post('/login/admin', adminLogin);

/**
 * @route   POST /forgot-password
 * @desc    Initiate password reset process by sending reset email
 * @access  Public
 */
router.post('/forgot-password', forgotPassword);

/**
 * @route   POST /reset-password
 * @desc    Reset password using token received via email
 * @access  Public
 */
router.post('/reset-password', resetPassword);

/**
 * @route   POST /verify-email
 * @desc    Verify user's email address using token
 * @access  Public
 */
router.post('/verify-email', verifyEmail);

/**
 * @route   POST /send-verification-email
 * @desc    Resend verification email to user
 * @access  Public
 */
router.post('/send-verification-email', sendVerificationEmail);

/**
 * @route   POST /refresh-token
 * @desc    Refresh JWT access token using refresh token
 * @access  Public (with refresh token)
 */
router.post('/refresh-token', refreshToken);

/**
 * @route   POST /change-password
 * @desc    Allow authenticated users to change their password
 * @access  Protected (requires valid access token)
 */
router.post('/change-password', validateAccessToken, authenticateJWT, changePassword);

/**
 * @route   POST /logout
 * @desc    Log out user and invalidate refresh tokens or sessions
 * @access  Public (requires token for logout)
 */
router.post('/logout', logout);

export default router;
