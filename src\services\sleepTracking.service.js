import sleepTrackerModel from '../models/sleepRecord.model.js';
import { ValidationError, NotFoundError } from '../utils/errorHandler.js';
import { ValidationMessages } from '../constants/messages.js';
import { HttpStatus } from '../constants/httpStatus.js';
import SleepRecordSerializer from '../serializers/sleepRecordSerializer.js';

class SleepTrackingService {
  static async getSleepRecords(req, res) {
    const userId = req.user;

    const sleepRecords = await sleepTrackerModel.find({ userId }).sort({ sleepDate: -1 }).lean();

    if (!sleepRecords) {
      throw new NotFoundError('Sleep records not found');
    }

    const formattedRecords = sleepRecords.map((record) => SleepRecordSerializer(record));

    return formattedRecords;
  }

  static async getSleepRecordById(req, res) {
    const { id } = req.params;

    const sleepRecord = await sleepTrackerModel.findById(id).lean();

    if (!sleepRecord) {
      throw new NotFoundError('Sleep record not found');
    }

    return SleepRecordSerializer(sleepRecord);
  }

  static async updateSleepRecord(req, res) {
    const userId = req.user;
    const allowedFields = ['sleepDuration', 'awakeTime', 'sleepTime', 'notes'];

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const updates = {};
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }

    if (Object.keys(updates).length === 0) {
      throw new ValidationError('No valid fields provided for update', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const sleepRecord = await sleepTrackerModel
      .findOneAndUpdate({ userId }, updates, {
        new: true,
        runValidators: true,
      })
      .lean();

    if (!sleepRecord) {
      throw new NotFoundError('Sleep record not found');
    }

    return SleepRecordSerializer(sleepRecord);
  }

  static async getSleepRecordsRange(req, res) {
    const userId = req.user;
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      throw new ValidationError('Start date and end date are required', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      throw new ValidationError('Start date cannot be after end date', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const sleepRecords = await sleepTrackerModel
      .find({
        userId,
        sleepDate: {
          $gte: start,
          $lte: end,
        },
      })
      .lean();

    if (!sleepRecords) {
      throw new NotFoundError('Sleep records not found');
    }

    const formattedRecords = sleepRecords.map((record) => SleepRecordSerializer(record));

    return formattedRecords;
  }
}

export default SleepTrackingService;
