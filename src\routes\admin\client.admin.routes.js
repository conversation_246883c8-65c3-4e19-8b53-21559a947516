import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  createClientByAdmin,
  getClientsList,
  getClientById,
  updateClientByAdmin,
  deleteClientByAdmin,
  deactivateClientByAdmin,
  activateClientByAdmin,
} from '../../controllers/clients.controller.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';

const router = express.Router();

// Global middleware stack: only ADMINs can access these routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

// Create client account by admin
router.post('/', createClientByAdmin);

/**
 * @route   GET /api/admin/clients
 * @desc    Fetch all clients in the system
 * @access  Admin only
 */
router.get('/', getClientsList);

/**
 * @route   GET /api/admin/clients/:id
 * @desc    Fetch a specific client by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getClientById);

/**
 * @route   PUT /api/admin/clients/:id
 * @desc    Update a client’s data (admin privilege)
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updateClientByAdmin);

/**
 * @route   PATCH /api/admin/clients/:id/deactivate
 * @desc    Deactivate a client's account
 * @access  Admin only
 */
router.patch('/:id/deactivate', validateMongoId(), deactivateClientByAdmin);

/**
 * @route   PATCH /api/admin/clients/:id/activate
 * @desc    Activate a client's account
 * @access  Admin only
 */
router.patch('/:id/activate', validateMongoId(), activateClientByAdmin);

/**
 * @route   DELETE /api/admin/clients/:id
 * @desc    Permanently delete a client from the system
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deleteClientByAdmin);

export default router;
