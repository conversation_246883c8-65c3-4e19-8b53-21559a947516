import planDurationOptionModel from '../../models/pricing/planDurationOption.modal.js';
import { ValidationMessages } from '../../constants/messages.js';
import { ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import { planDurationOptionSerializer } from '../../serializers/pricingPlanSerializer.js';
import mongoose from 'mongoose';

class PlanDurationOptionService {
  static async createPlanDurationOption(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { label, valueInDays, price, features } = req.body;

    if (!label || !valueInDays || !price || !features) {
      throw new ValidationError('Label, valueInDays, price, features and slug are required fields');
    }

    const slug = label.toLowerCase().replace(/ /g, '-');

    const existingOption = await planDurationOptionModel.findOne({ slug });
    if (existingOption) {
      throw new ValidationError('Duration option with this slug already exists', HttpStatus.STATUS_CODE.CONFLICT);
    }

    if (!Array.isArray(features)) {
      throw new ValidationError('Features must be an array');
    }

    features.forEach((feature) => {
      if (!mongoose.Types.ObjectId.isValid(feature)) {
        throw new ValidationError('Invalid feature ID format');
      }
    });

    if (typeof price !== 'number' || price <= 0) {
      throw new ValidationError('Price must be a positive number');
    }

    if (typeof valueInDays !== 'number' || valueInDays <= 0) {
      throw new ValidationError('Value in days must be a positive number');
    }

    const newPlanDurationOption = new planDurationOptionModel({
      label,
      valueInDays,
      price,
      features,
      slug,
    });

    await newPlanDurationOption.save();

    return planDurationOptionSerializer(newPlanDurationOption);
  }

  static async getPlanDurationOptions(req, res) {
    const planDurationOptions = await planDurationOptionModel.find({}).lean();

    if (!planDurationOptions) {
      throw new NotFoundError('Plan duration options not found');
    }

    const formattedOptions = planDurationOptions.map((option) => planDurationOptionSerializer(option));

    return formattedOptions;
  }

  static async getPlanDurationOptionById(req, res) {
    const { id } = req.params;

    const planDurationOption = await planDurationOptionModel.findById(id);

    if (!planDurationOption) {
      throw new NotFoundError('Plan duration option not found');
    }

    return planDurationOptionSerializer(planDurationOption);
  }

  static async updatePlanDurationOption(req, res) {
    const { id } = req.params;
    const updates = req.body;

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const planDurationOption = await planDurationOptionModel.findById(id);

    if (!planDurationOption) {
      throw new NotFoundError('Plan duration option not found');
    }

    Object.assign(planDurationOption, updates);
    await planDurationOption.save();

    return planDurationOptionSerializer(planDurationOption);
  }

  static async deletePlanDurationOption(req, res) {
    const { id } = req.params;

    const planDurationOption = await planDurationOptionModel.findById(id);

    if (!planDurationOption) {
      throw new NotFoundError('Plan duration option not found');
    }

    await planDurationOptionModel.findByIdAndDelete(id);

    return null;
  }
}

export default PlanDurationOptionService;
