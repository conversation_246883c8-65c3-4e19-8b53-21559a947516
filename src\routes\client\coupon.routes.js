import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import { validateCoupon } from '../../controllers/order/coupon.controller.js';

const router = express.Router();

router.use(validateAccessToken, authenticateJWT);

/**
 * @route   POST /api/coupons/validate
 * @desc    Validate a coupon code
 * @access  Client only
 */
router.post('/validate', validateCoupon);

export default router;
