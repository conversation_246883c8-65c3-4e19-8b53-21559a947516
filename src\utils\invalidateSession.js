import userRefreshTokenModel from '../models/userRefreshToken.modal.js';
import { TokenConstants } from '../constants/tokenConstants.js';

/**
 * Invalidate the current user session by blacklisting the refresh token.
 *
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 */
const invalidateSession = async (req, res) => {
  try {
    const refreshToken = req.cookies?.[TokenConstants.REFRESH_TOKEN];

    if (!refreshToken) {
      return res.status(400).json({ message: 'No refresh token found in cookies.' });
    }

    const result = await userRefreshTokenModel.findOneAndUpdate(
      { token: refreshToken },
      { $set: { blacklisted: true } }
    );

    if (!result) {
      return res.status(404).json({ message: 'Refresh token not found or already invalidated.' });
    }

    // Successful invalidation response
    return res.status(200).json({ message: 'Session invalidated successfully.' });
  } catch (error) {
    // Log error and respond with server error status
    console.error('Error invalidating session:', error);
    return res.status(500).json({ message: 'Internal server error while invalidating session.' });
  }
};

export default invalidateSession;
