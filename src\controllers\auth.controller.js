import asyncHandler from '../utils/asyncHandler.js';
import AuthService from '../services/auth.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import successResponse from '../utils/successResponse.js';

const register = asyncHandler(async (req, res) => {
  const user = await AuthService.register(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.CREATED, 'User registered successfully', user);
});

const sendVerificationEmail = asyncHandler(async (req, res) => {
  const result = await AuthService.sendVerificationEmail(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Verification email sent successfully', result);
});

const verifyEmail = asyncHandler(async (req, res) => {
  const result = await AuthService.verifyEmail(req, res);

  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Email verified successfully', result);
});

const login = asyncHandler(async (req, res) => {
  const user = await AuthService.login(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Login successful', user);
});

const adminLogin = asyncHandler(async (req, res) => {
  const user = await AuthService.adminLogin(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Login successful', user);
});

const forgotPassword = asyncHandler(async (req, res) => {
  const result = await AuthService.forgotPassword(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'OTP sent successfully', result);
});

const resetPassword = asyncHandler(async (req, res) => {
  const result = await AuthService.resetPassword(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Password reset successful', result);
});

const refreshToken = asyncHandler(async (req, res) => {
  const user = await AuthService.refreshToken(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'New tokens generated successfully', user);
});

const changePassword = asyncHandler(async (req, res) => {
  const result = await AuthService.changePassword(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Password changed successfully', result);
});

const logout = asyncHandler(async (req, res) => {
  const result = await AuthService.logout(req, res);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Logout successful', result);
});

export {
  register,
  sendVerificationEmail,
  verifyEmail,
  login,
  adminLogin,
  forgotPassword,
  resetPassword,
  changePassword,
  refreshToken,
  logout,
};
