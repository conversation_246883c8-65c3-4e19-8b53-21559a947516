import asyncHandler from '../utils/asyncHandler.js';
import AuthService from '../services/auth.service.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { ValidationMessages } from '../constants/messages.js';

const register = asyncHandler(async (req, res) => {
  const user = await AuthService.register(req, res);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'User registered successfully',
    data: user,
  });
});

const sendVerificationEmail = asyncHandler(async (req, res) => {
  const result = await AuthService.sendVerificationEmail(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.VERIFICATION_EMAIL_SENT,
    data: result,
  });
});

const verifyEmail = asyncHandler(async (req, res) => {
  const result = await AuthService.verifyEmail(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.EMAIL_VERIFIED,
    data: result,
  });
});

const login = asyncHandler(async (req, res) => {
  const user = await AuthService.login(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.ACCOUNT_LOGIN_SUCCESS,
    data: user,
  });
});

const adminLogin = asyncHandler(async (req, res) => {
  const user = await AuthService.adminLogin(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.ACCOUNT_LOGIN_SUCCESS,
    data: user,
  });
});

const forgotPassword = asyncHandler(async (req, res) => {
  const result = await AuthService.forgotPassword(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.OTP_SENT,
    data: result,
  });
});

const resetPassword = asyncHandler(async (req, res) => {
  const result = await AuthService.resetPassword(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.PASSWORD_RESET_SUCCESS,
    data: result,
  });
});

const refreshToken = asyncHandler(async (req, res) => {
  const user = await AuthService.refreshToken(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'New tokens generated successfully',
    data: user,
  });
});

const changePassword = asyncHandler(async (req, res) => {
  const result = await AuthService.changePassword(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.PASSWORD_CHANGED_SUCCESS,
    data: result,
  });
});

const logout = asyncHandler(async (req, res) => {
  const result = await AuthService.logout(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: ValidationMessages.AUTHENTICATION.LOGOUT_SUCCESS,
    data: result,
  });
});

export {
  register,
  sendVerificationEmail,
  verifyEmail,
  login,
  adminLogin,
  forgotPassword,
  resetPassword,
  changePassword,
  refreshToken,
  logout,
};
