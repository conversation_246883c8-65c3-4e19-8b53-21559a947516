import mongoose from 'mongoose';
import { PaymentStatus, PricingUnit } from '../../constants/enums.js';

const planPurchaseSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PricingPlan',
      required: true,
    },
    durationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'PlanDurationOption',
      required: true,
    },
    razorpay_order_id: {
      type: String,
      required: true,
    },
    razorpay_payment_id: {
      type: String,
    },
    razorpay_signature: {
      type: String,
    },
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'INR',
    },
    paymentType: {
      type: String,
      enum: PricingUnit.ALL,
      default: PricingUnit.ONE_TIME,
    },
    status: {
      type: String,
      enum: PaymentStatus.ALL,
      required: true,
      default: PaymentStatus.CREATED,
    },
    paymentDate: {
      type: Date,
      default: null,
    },
    rawResponse: {
      type: Object,
      default: null,
    },
    metadata: {
      type: Object,
      default: null,
    },
  },
  { timestamps: true }
);

export default mongoose.models.PlanPurchase || mongoose.model('PlanPurchase', planPurchaseSchema, 'plan_purchases');
