{"name": "food-for-soul-tech", "version": "1.0.0", "description": "", "private": true, "engines": {"node": ">=18.20.4 <22.14.0", "npm": ">=8.0.0 <10.10.0"}, "main": "./src/server.js", "scripts": {"start": "node ./src/server.js", "dev": "nodemon ./src/server.js", "format": "prettier --write .", "lint": "npx eslint ."}, "keywords": [], "author": "", "license": "ISC", "type": "module", "devDependencies": {"nodemon": "^3.1.0", "prettier": "^3.5.3"}, "dependencies": {"bcrypt": "^5.1.1", "chalk": "^5.4.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "express": "^5.1.0", "express-rate-limit": "^7.1.5", "helmet": "^8.1.0", "mongoose": "^8.3.4", "morgan": "^1.10.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "razorpay": "^2.9.6", "winston": "^3.17.0"}}