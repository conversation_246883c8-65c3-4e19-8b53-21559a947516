<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Food for Soul Tech - Health Monitoring Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <i class="fas fa-heart"></i>
          <span>Food for Soul Tech</span>
        </div>
        <div class="nav-links">
          <a href="#overview" class="nav-link">Overview</a>
          <a href="#health" class="nav-link">Health Monitor</a>
          <a href="#metrics" class="nav-link">Live Metrics</a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="gradient-text">Food for Soul Tech</span>
            <br />System Health Observatory
          </h1>
          <p class="hero-description">
            Advanced real-time monitoring and diagnostics platform for comprehensive system health analysis. Track
            performance metrics, monitor service availability, analyze system resources, and ensure optimal platform
            reliability with intelligent alerting and detailed analytics.
          </p>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number" id="hero-endpoints">3</span>
              <span class="stat-label">Health Endpoints</span>
            </div>
            <div class="stat">
              <span class="stat-number" id="hero-uptime">99.9%</span>
              <span class="stat-label">Uptime</span>
            </div>
            <div class="stat">
              <span class="stat-number" id="hero-response-time">--</span>
              <span class="stat-label">Avg Response</span>
            </div>
            <div class="stat">
              <span class="stat-number" id="hero-status">Operational</span>
              <span class="stat-label">System Status</span>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="system-status-preview">
            <div class="status-indicator" id="hero-system-indicator">
              <div class="status-dot online"></div>
              <span>System Operational</span>
            </div>
            <div class="metrics-preview">
              <div class="metric-preview">
                <i class="fas fa-server"></i>
                <span id="hero-api-status">APIs Online</span>
              </div>
              <div class="metric-preview">
                <i class="fas fa-database"></i>
                <span id="hero-db-status">Database Connected</span>
              </div>
              <div class="metric-preview">
                <i class="fas fa-memory"></i>
                <span id="hero-memory-status">Memory: Normal</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Health Check Section -->
    <section id="health" class="health">
      <div class="container">
        <h2 class="section-title">Comprehensive System Health Dashboard</h2>

        <!-- Primary Health Indicators -->
        <div class="primary-health-grid">
          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-server"></i>
              </div>
              <div class="health-info">
                <h3>System Status</h3>
                <div class="health-status online" id="system-status">
                  <i class="fas fa-circle"></i>
                  <span>Operational</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Overall Health</span>
                <span class="metric-value" id="overall-health">Excellent</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Active Services</span>
                <span class="metric-value" id="active-services">3/3</span>
              </div>
            </div>
          </div>

          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-tachometer-alt"></i>
              </div>
              <div class="health-info">
                <h3>Performance Metrics</h3>
                <div class="health-status online" id="performance-status">
                  <i class="fas fa-circle"></i>
                  <span>Optimal</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Avg Response Time</span>
                <span class="metric-value" id="response-time">--ms</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Memory Usage</span>
                <span class="metric-value" id="memory-value">--</span>
              </div>
            </div>
          </div>

          <div class="primary-health-card">
            <div class="health-header">
              <div class="health-icon">
                <i class="fas fa-database"></i>
              </div>
              <div class="health-info">
                <h3>Database & Services</h3>
                <div class="health-status online" id="database-status">
                  <i class="fas fa-circle"></i>
                  <span>Connected</span>
                </div>
              </div>
            </div>
            <div class="health-metrics">
              <div class="metric-row">
                <span class="metric-label">Connection Pool</span>
                <span class="metric-value" id="connection-pool">Active</span>
              </div>
              <div class="metric-row">
                <span class="metric-label">Query Performance</span>
                <span class="metric-value" id="query-performance">Optimal</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Health Monitoring -->
        <div class="detailed-health-section">
          <h3>Detailed Health Monitoring</h3>
          <div class="health-endpoints-grid">
            <div class="endpoint-card">
              <div class="endpoint-header">
                <div class="endpoint-method">GET</div>
                <div class="endpoint-path">/api-health</div>
                <div class="health-status online" id="basic-health-status">
                  <i class="fas fa-circle"></i>
                  <span>Online</span>
                </div>
              </div>
              <div class="endpoint-description">Basic health check for load balancer verification</div>
              <div id="basic-health-result" class="health-result"></div>
            </div>

            <div class="endpoint-card">
              <div class="endpoint-header">
                <div class="endpoint-method">GET</div>
                <div class="endpoint-path">/api/health</div>
                <div class="health-status online" id="comprehensive-health-status">
                  <i class="fas fa-circle"></i>
                  <span>Online</span>
                </div>
              </div>
              <div class="endpoint-description">Comprehensive system metrics and diagnostics</div>
              <div id="comprehensive-health-result" class="health-result"></div>
            </div>

            <div class="endpoint-card">
              <div class="endpoint-header">
                <div class="endpoint-method">GET</div>
                <div class="endpoint-path">/api/health/detailed</div>
                <div class="health-status online" id="detailed-health-status">
                  <i class="fas fa-circle"></i>
                  <span>Online</span>
                </div>
              </div>
              <div class="endpoint-description">Extended diagnostics with performance analytics</div>
              <div id="detailed-health-result" class="health-result"></div>
            </div>
          </div>
        </div>

        <!-- Live System Metrics -->
        <div class="live-metrics-dashboard">
          <h3>Live System Metrics</h3>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Server Uptime</span>
                <span class="metric-value" id="uptime-value">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-memory"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Memory Usage</span>
                <span class="metric-value" id="memory-usage">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-wifi"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">API Status</span>
                <span class="metric-value" id="api-status">--</span>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">
                <i class="fas fa-heartbeat"></i>
              </div>
              <div class="metric-content">
                <span class="metric-label">Health Score</span>
                <span class="metric-value" id="health-score">--</span>
              </div>
            </div>
          </div>
          <div class="auto-refresh-indicator">
            <i class="fas fa-sync-alt fa-spin"></i>
            <span>Auto-refreshing every 30 seconds</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <i class="fas fa-heart"></i>
            <span>Food for Soul Tech</span>
          </div>
          <div class="footer-links">
            <a href="#overview">Overview</a>
            <a href="#health">Health Monitor</a>
            <a href="#metrics">Live Metrics</a>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Food for Soul Tech. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
  </body>
</html>
