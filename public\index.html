<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Food for Soul Tech - API Documentation</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <i class="fas fa-heart"></i>
          <span>Food for Soul Tech</span>
        </div>
        <div class="nav-links">
          <a href="#overview" class="nav-link">Overview</a>
          <a href="#apis" class="nav-link">APIs</a>
          <a href="#health" class="nav-link">Health</a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="gradient-text">Food for Soul Tech</span>
            <br />API Documentation
          </h1>
          <p class="hero-description">
            Comprehensive wellness and nutrition management platform with powerful APIs for client management, sleep
            tracking, pricing, and administrative functions.
          </p>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">64</span>
              <span class="stat-label">API Endpoints</span>
            </div>
            <div class="stat">
              <span class="stat-number">10</span>
              <span class="stat-label">Categories</span>
            </div>
            <div class="stat">
              <span class="stat-number">100%</span>
              <span class="stat-label">Documented</span>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="api-card-preview">
            <div class="api-method get">GET</div>
            <span>/api/clients/me</span>
          </div>
          <div class="api-card-preview">
            <div class="api-method post">POST</div>
            <span>/api/auth/login</span>
          </div>
          <div class="api-card-preview">
            <div class="api-method put">PUT</div>
            <span>/api/pricing/plans</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Overview Section -->
    <section id="overview" class="overview">
      <div class="container">
        <h2 class="section-title">Platform Overview</h2>
        <div class="overview-grid">
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3>Secure Authentication</h3>
            <p>JWT-based authentication with role-based access control for clients and administrators.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>Client Management</h3>
            <p>Comprehensive client profile management with onboarding, settings, and health tracking.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-moon"></i>
            </div>
            <h3>Sleep Tracking</h3>
            <p>Advanced sleep monitoring and analytics for better health insights and recommendations.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-tags"></i>
            </div>
            <h3>Pricing & Coupons</h3>
            <p>Flexible pricing plans with duration options, features, and promotional coupon system.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- API Documentation Section -->
    <section id="apis" class="apis">
      <div class="container">
        <h2 class="section-title">API Documentation</h2>
        <div class="api-categories">
          <!-- Authentication APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-key"></i>
              <h3>Authentication APIs</h3>
              <span class="endpoint-count">9 endpoints</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/signup</div>
                <div class="endpoint-desc">Register a new user (client or admin)</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/login</div>
                <div class="endpoint-desc">User login for standard users (clients)</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/login/admin</div>
                <div class="endpoint-desc">Admin user login with elevated privileges</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/forgot-password</div>
                <div class="endpoint-desc">Initiate password reset process</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/reset-password</div>
                <div class="endpoint-desc">Reset password using token</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/verify-email</div>
                <div class="endpoint-desc">Verify user's email address</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/send-verification-email</div>
                <div class="endpoint-desc">Resend verification email</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/refresh-token</div>
                <div class="endpoint-desc">Refresh JWT access token</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/change-password</div>
                <div class="endpoint-desc">Change user password</div>
                <div class="endpoint-access protected">Protected</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/auth/logout</div>
                <div class="endpoint-desc">Log out user and invalidate tokens</div>
                <div class="endpoint-access public">Public</div>
              </div>
            </div>
          </div>

          <!-- Client Management APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-user"></i>
              <h3>Client Management APIs</h3>
              <span class="endpoint-count">5 endpoints</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/clients/onboarding-process</div>
                <div class="endpoint-desc">Submit onboarding data during first-time setup</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/clients/me</div>
                <div class="endpoint-desc">Retrieve authenticated client's profile</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/clients/me</div>
                <div class="endpoint-desc">Update authenticated client's profile</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/clients/me/settings</div>
                <div class="endpoint-desc">Get client-specific user settings</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/clients/me/settings</div>
                <div class="endpoint-desc">Update client-specific user settings</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
            </div>
          </div>

          <!-- Admin Client Management APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-users-cog"></i>
              <h3>Admin Client Management APIs</h3>
              <span class="endpoint-count">7 endpoints</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/admin/clients</div>
                <div class="endpoint-desc">Create client account by admin</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/clients</div>
                <div class="endpoint-desc">Fetch all clients in the system</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/clients/:id</div>
                <div class="endpoint-desc">Fetch a specific client by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/admin/clients/:id</div>
                <div class="endpoint-desc">Update a client's data (admin privilege)</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/admin/clients/:id/deactivate</div>
                <div class="endpoint-desc">Deactivate a client's account</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/admin/clients/:id/activate</div>
                <div class="endpoint-desc">Activate a client's account</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method delete">DELETE</div>
                <div class="endpoint-path">/api/admin/clients/:id</div>
                <div class="endpoint-desc">Permanently delete a client from the system</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
            </div>
          </div>

          <!-- Sleep Tracking APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-moon"></i>
              <h3>Sleep Tracking APIs</h3>
              <span class="endpoint-count">4 endpoints</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/clients/sleep-tracking</div>
                <div class="endpoint-desc">Get all sleep records of the authenticated client</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/clients/sleep-tracking/range</div>
                <div class="endpoint-desc">Get sleep records within a date range</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/clients/sleep-tracking</div>
                <div class="endpoint-desc">Update a sleep record for the authenticated client</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/clients/sleep-tracking/:id</div>
                <div class="endpoint-desc">Fetch a specific sleep record by ID (Admin view)</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
            </div>
          </div>

          <!-- Pricing Management APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-tags"></i>
              <h3>Pricing Management APIs</h3>
              <span class="endpoint-count">15 endpoints</span>
            </div>
            <div class="api-endpoints">
              <!-- Plan Features -->
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/pricing/features</div>
                <div class="endpoint-desc">Create a new plan feature</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/features</div>
                <div class="endpoint-desc">Retrieve all plan features</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/features/:id</div>
                <div class="endpoint-desc">Get a specific plan feature by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/pricing/features/:id</div>
                <div class="endpoint-desc">Update details of a plan feature</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method delete">DELETE</div>
                <div class="endpoint-path">/api/pricing/features/:id</div>
                <div class="endpoint-desc">Delete a plan feature by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <!-- Plan Durations -->
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/pricing/durations</div>
                <div class="endpoint-desc">Create a new plan duration option</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/durations</div>
                <div class="endpoint-desc">Fetch all available plan duration options</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/durations/:id</div>
                <div class="endpoint-desc">Get a specific plan duration option by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/pricing/durations/:id</div>
                <div class="endpoint-desc">Update a specific plan duration option</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method delete">DELETE</div>
                <div class="endpoint-path">/api/pricing/durations/:id</div>
                <div class="endpoint-desc">Delete a specific plan duration option</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <!-- Pricing Plans -->
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/pricing/plans</div>
                <div class="endpoint-desc">Create a new pricing plan</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/plans</div>
                <div class="endpoint-desc">Get all pricing plans</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/pricing/plans/:id</div>
                <div class="endpoint-desc">Get a specific pricing plan by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/pricing/plans/:id</div>
                <div class="endpoint-desc">Update a specific pricing plan by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method delete">DELETE</div>
                <div class="endpoint-path">/api/pricing/plans/:id</div>
                <div class="endpoint-desc">Delete a specific pricing plan by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
            </div>
          </div>

          <!-- Payment & Subscription APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-credit-card"></i>
              <h3>Payment & Subscription APIs</h3>
              <span class="endpoint-count">12 endpoints</span>
            </div>
            <div class="api-endpoints">
              <!-- Payment APIs -->
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/payments/plans</div>
                <div class="endpoint-desc">Get available pricing plans for purchase</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/payments/create-order</div>
                <div class="endpoint-desc">Create a payment order for plan purchase</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/payments/verify</div>
                <div class="endpoint-desc">Verify payment and activate subscription</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/payments/validate-coupon</div>
                <div class="endpoint-desc">Validate a coupon code and calculate discount</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/payments/history</div>
                <div class="endpoint-desc">Get user's payment history</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/payments/:orderId</div>
                <div class="endpoint-desc">Get payment details by order ID</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <!-- Subscription APIs -->
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/subscriptions/active</div>
                <div class="endpoint-desc">Get user's active subscription</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/subscriptions/history</div>
                <div class="endpoint-desc">Get user's subscription history</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/subscriptions/:subscriptionId</div>
                <div class="endpoint-desc">Get subscription details by ID</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/subscriptions/cancel</div>
                <div class="endpoint-desc">Cancel user's active subscription</div>
                <div class="endpoint-access client">Client Only</div>
              </div>
            </div>
          </div>

          <!-- Coupon Management APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-ticket-alt"></i>
              <h3>Coupon Management APIs</h3>
              <span class="endpoint-count">8 endpoints</span>
            </div>
            <div class="api-endpoints">
              <!-- Admin Coupon Management -->
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/admin/coupons</div>
                <div class="endpoint-desc">Create a new coupon</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/coupons</div>
                <div class="endpoint-desc">Get all coupons</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/coupons/:id</div>
                <div class="endpoint-desc">Get a specific coupon by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method put">PUT</div>
                <div class="endpoint-path">/api/admin/coupons/:id</div>
                <div class="endpoint-desc">Update a specific coupon by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method delete">DELETE</div>
                <div class="endpoint-path">/api/admin/coupons/:id</div>
                <div class="endpoint-desc">Delete a specific coupon by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/admin/coupons/:id/activate</div>
                <div class="endpoint-desc">Activate a coupon</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/admin/coupons/:id/deactivate</div>
                <div class="endpoint-desc">Deactivate a coupon</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <!-- Client Coupon Usage -->
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/coupons/validate</div>
                <div class="endpoint-desc">Validate a coupon code</div>
                <div class="endpoint-access protected">Protected</div>
              </div>
            </div>
          </div>

          <!-- Admin Payment & Subscription Management APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-money-check-alt"></i>
              <h3>Admin Payment & Subscription Management APIs</h3>
              <span class="endpoint-count">10 endpoints</span>
            </div>
            <div class="api-endpoints">
              <!-- Payment Management -->
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/payments</div>
                <div class="endpoint-desc">Get all payment orders with filters</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/payments/stats</div>
                <div class="endpoint-desc">Get payment statistics</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/payments/:orderId</div>
                <div class="endpoint-desc">Get payment order details by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <!-- Subscription Management -->
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/subscriptions</div>
                <div class="endpoint-desc">Get all subscriptions with filters</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/subscriptions/stats</div>
                <div class="endpoint-desc">Get subscription statistics</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method post">POST</div>
                <div class="endpoint-path">/api/admin/subscriptions/update-expired</div>
                <div class="endpoint-desc">Update expired subscriptions</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/subscriptions/:subscriptionId</div>
                <div class="endpoint-desc">Get subscription details by ID</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method patch">PATCH</div>
                <div class="endpoint-path">/api/admin/subscriptions/:subscriptionId/toggle</div>
                <div class="endpoint-desc">Toggle subscription status (activate/deactivate)</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
            </div>
          </div>

          <!-- Dashboard APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-chart-bar"></i>
              <h3>Dashboard APIs</h3>
              <span class="endpoint-count">1 endpoint</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/admin/dashboard</div>
                <div class="endpoint-desc">Fetch dashboard overview data</div>
                <div class="endpoint-access admin">Admin Only</div>
              </div>
            </div>
          </div>

          <!-- Health Check APIs -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-heartbeat"></i>
              <h3>Health Check APIs</h3>
              <span class="endpoint-count">2 endpoints</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api-health</div>
                <div class="endpoint-desc">Check API server health status</div>
                <div class="endpoint-access public">Public</div>
              </div>
              <div class="api-endpoint">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/</div>
                <div class="endpoint-desc">Root endpoint - serves this welcome page</div>
                <div class="endpoint-access public">Public</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Health Check Section -->
    <section id="health" class="health">
      <div class="container">
        <h2 class="section-title">System Health</h2>
        <div class="health-grid">
          <div class="health-card">
            <div class="health-status online" id="api-status">
              <i class="fas fa-circle"></i>
              <span>API Online</span>
            </div>
            <p>Main API service is running</p>
            <button class="health-check-btn" onclick="checkHealth('/api-health')">Check Health</button>
          </div>
          <div class="health-card">
            <div class="health-status online" id="db-status">
              <i class="fas fa-circle"></i>
              <span>Database Connected</span>
            </div>
            <p>Database connection is active</p>
            <button class="health-check-btn" onclick="checkHealth('/')">Check Root</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <i class="fas fa-heart"></i>
            <span>Food for Soul Tech</span>
          </div>
          <div class="footer-links">
            <a href="#overview">Overview</a>
            <a href="#apis">APIs</a>
            <a href="#health">Health</a>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Food for Soul Tech. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
  </body>
</html>
