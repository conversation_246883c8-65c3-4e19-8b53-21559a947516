<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Food for Soul Tech - Health Monitoring Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="css/styles.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <i class="fas fa-heart"></i>
          <span>Food for Soul Tech</span>
        </div>
        <div class="nav-links">
          <a href="#overview" class="nav-link">Overview</a>
          <a href="#health" class="nav-link">Health Monitor</a>
          <a href="#endpoints" class="nav-link">Health APIs</a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="gradient-text">Food for Soul Tech</span>
            <br />Health Monitoring Dashboard
          </h1>
          <p class="hero-description">
            Real-time system health monitoring and diagnostics for the Food for Soul Tech wellness platform. Monitor API
            status, database connectivity, system performance, and service availability.
          </p>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">3</span>
              <span class="stat-label">Health Endpoints</span>
            </div>
            <div class="stat">
              <span class="stat-number">24/7</span>
              <span class="stat-label">Monitoring</span>
            </div>
            <div class="stat">
              <span class="stat-number">Real-time</span>
              <span class="stat-label">Status</span>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div class="api-card-preview">
            <div class="api-method get">GET</div>
            <span>/api-health</span>
          </div>
          <div class="api-card-preview">
            <div class="api-method get">GET</div>
            <span>/api/health</span>
          </div>
          <div class="api-card-preview">
            <div class="api-method get">GET</div>
            <span>/api/health/detailed</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Overview Section -->
    <section id="overview" class="overview">
      <div class="container">
        <h2 class="section-title">Health Monitoring Overview</h2>
        <div class="overview-grid">
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-heartbeat"></i>
            </div>
            <h3>Basic Health Check</h3>
            <p>Simple endpoint for monitoring service availability and basic server status verification.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>Comprehensive Metrics</h3>
            <p>Detailed system information including memory usage, uptime, environment, and service status.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-tachometer-alt"></i>
            </div>
            <h3>Performance Analytics</h3>
            <p>Extended diagnostics with CPU usage, response times, and advanced system health checks.</p>
          </div>
          <div class="overview-card">
            <div class="card-icon">
              <i class="fas fa-database"></i>
            </div>
            <h3>Service Monitoring</h3>
            <p>Real-time monitoring of database connectivity, API services, and system dependencies.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Health Endpoints Section -->
    <section id="endpoints" class="apis">
      <div class="container">
        <h2 class="section-title">Health Check Endpoints</h2>
        <div class="api-categories">
          <!-- Basic Health Check -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-heartbeat"></i>
              <h3>Basic Health Check</h3>
              <span class="endpoint-count">1 endpoint</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint" onclick="testHealthEndpoint('/api-health')">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api-health</div>
                <div class="endpoint-desc">
                  Simple health check for basic monitoring and load balancer health verification
                </div>
                <div class="endpoint-access public">Public</div>
              </div>
            </div>
            <div class="endpoint-details">
              <h4>Response Example:</h4>
              <pre><code>{
  "status": "Success",
  "message": "The server is running and healthy."
}</code></pre>
              <p><strong>Use Case:</strong> Perfect for load balancers, monitoring tools, and simple uptime checks.</p>
            </div>
          </div>

          <!-- Comprehensive Health Check -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-chart-line"></i>
              <h3>Comprehensive Health Check</h3>
              <span class="endpoint-count">1 endpoint</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint" onclick="testHealthEndpoint('/api/health')">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/health</div>
                <div class="endpoint-desc">
                  Detailed health check with system metrics, memory usage, and service status
                </div>
                <div class="endpoint-access public">Public</div>
              </div>
            </div>
            <div class="endpoint-details">
              <h4>Response Example:</h4>
              <pre><code>{
  "status": "Success",
  "message": "The server is running and healthy.",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-17T02:42:19.494Z",
    "uptime": 54.5752637,
    "environment": "development",
    "version": "v22.13.1",
    "memory": {
      "used": 25.67,
      "total": 35.84,
      "external": 2.15
    },
    "services": {
      "database": "connected",
      "server": "running"
    },
    "system": {
      "platform": "win32",
      "arch": "x64",
      "pid": 12345
    }
  }
}</code></pre>
              <p>
                <strong>Use Case:</strong> Ideal for monitoring dashboards and system administrators who need detailed
                metrics.
              </p>
            </div>
          </div>

          <!-- Extended Health Check -->
          <div class="api-category">
            <div class="category-header">
              <i class="fas fa-tachometer-alt"></i>
              <h3>Extended Health Check</h3>
              <span class="endpoint-count">1 endpoint</span>
            </div>
            <div class="api-endpoints">
              <div class="api-endpoint" onclick="testHealthEndpoint('/api/health/detailed')">
                <div class="endpoint-method get">GET</div>
                <div class="endpoint-path">/api/health/detailed</div>
                <div class="endpoint-desc">
                  Extended health check with performance metrics, CPU usage, and advanced diagnostics
                </div>
                <div class="endpoint-access public">Public</div>
              </div>
            </div>
            <div class="endpoint-details">
              <h4>Response Example:</h4>
              <pre><code>{
  "status": "Success",
  "message": "Detailed health check completed",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-17T02:42:19.494Z",
    "responseTime": 1.23,
    "uptime": {
      "seconds": 54.5752637,
      "formatted": "54 seconds"
    },
    "environment": {
      "nodeEnv": "development",
      "nodeVersion": "v22.13.1",
      "platform": "win32",
      "architecture": "x64",
      "processId": 12345
    },
    "memory": {
      "heapUsed": 25.67,
      "heapTotal": 35.84,
      "external": 2.15,
      "rss": 45.23,
      "arrayBuffers": 1.12
    },
    "cpu": {
      "user": 123456,
      "system": 78910
    },
    "services": {
      "database": "connected",
      "server": "running",
      "api": "operational"
    },
    "checks": {
      "database": true,
      "memory": true,
      "uptime": true
    }
  }
}</code></pre>
              <p>
                <strong>Use Case:</strong> Perfect for performance monitoring, debugging, and comprehensive system
                analysis.
              </p>
            </div>
          </div>

        </div>
      </div>
    </section>




        </div>
      </div>
    </section>

    <!-- Health Check Section -->
    <section id="health" class="health">
      <div class="container">
        <h2 class="section-title">Real-time Health Monitoring</h2>
        <div class="health-grid">
          <div class="health-card">
            <div class="health-status online" id="basic-health-status">
              <i class="fas fa-circle"></i>
              <span>Basic Health</span>
            </div>
            <p>Simple health check endpoint</p>
            <button class="health-check-btn" onclick="testHealthEndpoint('/api-health', 'basic-health-status', 'basic-health-result')">Test /api-health</button>
            <div id="basic-health-result" class="health-result"></div>
          </div>
          <div class="health-card">
            <div class="health-status online" id="comprehensive-health-status">
              <i class="fas fa-circle"></i>
              <span>Comprehensive Health</span>
            </div>
            <p>Detailed system metrics</p>
            <button class="health-check-btn" onclick="testHealthEndpoint('/api/health', 'comprehensive-health-status', 'comprehensive-health-result')">Test /api/health</button>
            <div id="comprehensive-health-result" class="health-result"></div>
          </div>
          <div class="health-card">
            <div class="health-status online" id="detailed-health-status">
              <i class="fas fa-circle"></i>
              <span>Extended Health</span>
            </div>
            <p>Performance analytics</p>
            <button class="health-check-btn" onclick="testHealthEndpoint('/api/health/detailed', 'detailed-health-status', 'detailed-health-result')">Test /api/health/detailed</button>
            <div id="detailed-health-result" class="health-result"></div>
          </div>
        </div>

        <!-- Real-time Status Dashboard -->
        <div class="status-dashboard">
          <h3>Live System Status</h3>
          <div class="status-metrics">
            <div class="metric">
              <span class="metric-label">Server Uptime</span>
              <span class="metric-value" id="uptime-value">--</span>
            </div>
            <div class="metric">
              <span class="metric-label">Memory Usage</span>
              <span class="metric-value" id="memory-value">--</span>
            </div>
            <div class="metric">
              <span class="metric-label">Response Time</span>
              <span class="metric-value" id="response-time-value">--</span>
            </div>
            <div class="metric">
              <span class="metric-label">Database Status</span>
              <span class="metric-value" id="database-status-value">--</span>
            </div>
          </div>
          <button class="refresh-btn" onclick="refreshAllHealthChecks()">
            <i class="fas fa-sync-alt"></i> Refresh All
          </button>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-brand">
            <i class="fas fa-heart"></i>
            <span>Food for Soul Tech</span>
          </div>
          <div class="footer-links">
            <a href="#overview">Overview</a>
            <a href="#endpoints">Health APIs</a>
            <a href="#health">Health Monitor</a>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Food for Soul Tech. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script src="js/script.js"></script>
  </body>
</html>
