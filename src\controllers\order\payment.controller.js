import PaymentService from '../../services/order/payment.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import successResponse from '../../utils/successResponse.js';

/**
 * Create a payment order for plan purchase
 * @route POST /api/payments/create-order
 * @access Client
 */
const createPaymentOrder = asyncHandler(async (req, res) => {
  const orderData = await PaymentService.createPaymentOrder(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Payment order created successfully', orderData);
});

/**
 * Verify payment and activate subscription
 * @route POST /api/payments/verify
 * @access Client
 */
const verifyPayment = asyncHandler(async (req, res) => {
  const paymentData = await PaymentService.verifyPayment(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Payment verified successfully', paymentData);
});

/**
 * Get user's payment history
 * @route GET /api/payments/history
 * @access Client
 */
const getPaymentHistory = asyncHandler(async (req, res) => {
  const historyData = await PaymentService.getPaymentHistory(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Payment history retrieved successfully', historyData);
});

/**
 * Get payment details by order ID
 * @route GET /api/payments/:orderId
 * @access Client
 */
const getPaymentDetails = asyncHandler(async (req, res) => {
  const paymentData = await PaymentService.getPaymentDetails(req);
  successResponse(res, HttpStatus.STATUS_CODE.OK, 'Payment details retrieved successfully', paymentData);
});

export { createPaymentOrder, verifyPayment, getPaymentHistory, getPaymentDetails };
