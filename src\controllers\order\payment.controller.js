import PaymentService from '../../services/order/payment.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';

/**
 * Create a payment order for plan purchase
 * @route POST /api/payments/create-order
 * @access Client
 */
const createPaymentOrder = asyncHandler(async (req, res) => {
  const orderData = await PaymentService.createPaymentOrder(req);

  res.status(HttpStatus.STATUS_CODE.CREATED).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Payment order created successfully',
    data: orderData,
  });
});

/**
 * Verify payment and activate subscription
 * @route POST /api/payments/verify
 * @access Client
 */
const verifyPayment = asyncHandler(async (req, res) => {
  const paymentData = await PaymentService.verifyPayment(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Payment verified successfully',
    data: paymentData,
  });
});

/**
 * Get user's payment history
 * @route GET /api/payments/history
 * @access Client
 */
const getPaymentHistory = asyncHandler(async (req, res) => {
  const historyData = await PaymentService.getPaymentHistory(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Payment history retrieved successfully',
    data: historyData,
  });
});

/**
 * Get payment details by order ID
 * @route GET /api/payments/:orderId
 * @access Client
 */
const getPaymentDetails = asyncHandler(async (req, res) => {
  const paymentData = await PaymentService.getPaymentDetails(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Payment details retrieved successfully',
    data: paymentData,
  });
});

/**
 * Validate coupon code
 * @route POST /api/payments/validate-coupon
 * @access Client
 */
const validateCoupon = asyncHandler(async (req, res) => {
  const { couponCode, amount } = req.body;
  const userId = req.user.id;

  if (!couponCode || !amount) {
    return res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      status: HttpStatus.STATUS.ERROR,
      message: 'Coupon code and amount are required',
    });
  }

  try {
    const couponResult = await PaymentService.applyCoupon(couponCode, userId, amount);
    
    res.status(HttpStatus.STATUS_CODE.OK).json({
      status: HttpStatus.STATUS.SUCCESS,
      message: 'Coupon is valid',
      data: {
        isValid: true,
        coupon: {
          code: couponResult.coupon.code,
          discountType: couponResult.coupon.discountType,
          discountValue: couponResult.coupon.discountValue,
        },
        originalAmount: amount,
        discountAmount: couponResult.discountAmount,
        finalAmount: couponResult.discountedAmount,
      },
    });
  } catch (error) {
    res.status(HttpStatus.STATUS_CODE.BAD_REQUEST).json({
      status: HttpStatus.STATUS.ERROR,
      message: error.message,
      data: {
        isValid: false,
        originalAmount: amount,
        finalAmount: amount,
      },
    });
  }
});

/**
 * Get available pricing plans for purchase
 * @route GET /api/payments/plans
 * @access Client
 */
const getAvailablePlans = asyncHandler(async (req, res) => {
  // Import here to avoid circular dependency
  const PricingPlanService = (await import('../../services/pricing/pricingPlans.sevice.js')).default;
  
  const plansData = await PricingPlanService.getPricingPlans(req, res);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Available plans retrieved successfully',
    data: plansData,
  });
});

export {
  createPaymentOrder,
  verifyPayment,
  getPaymentHistory,
  getPaymentDetails,
  validateCoupon,
  getAvailablePlans,
};
