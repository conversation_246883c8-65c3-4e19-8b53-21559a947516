import mongoose from 'mongoose';
import subscriptionModel from '../../models/order/subscription.model.js';
import planPurchaseModel from '../../models/order/order.modal.js';
import userModel from '../../models/user.model.js';
import { ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import { SubscriptionStatus } from '../../constants/enums.js';
import {
  subscriptionSerializer,
  subscriptionWithPlanSerializer,
  activeSubscriptionSerializer,
  subscriptionHistorySerializer,
  adminSubscriptionSerializer,
} from '../../serializers/subscriptionSerializer.js';

class SubscriptionService {
  /**
   * Get user's active subscription
   */
  static async getActiveSubscription(req) {
    const userId = req.user.id;

    const subscription = await subscriptionModel
      .findOne({ userId, isActive: true })
      .populate([
        {
          path: 'planId',
          select: 'name slug description isPopular',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price currency paymentType',
        },
        {
          path: 'features',
          select: 'name description slug',
        },
      ])
      .lean();

    return activeSubscriptionSerializer(subscription);
  }

  /**
   * Get user's subscription history
   */
  static async getSubscriptionHistory(req) {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    const skip = (page - 1) * limit;

    const subscriptions = await subscriptionModel
      .find({ userId })
      .populate([
        {
          path: 'planId',
          select: 'name slug',
        },
        {
          path: 'durationId',
          select: 'label valueInDays',
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await subscriptionModel.countDocuments({ userId });

    return {
      subscriptions: subscriptionHistorySerializer(subscriptions),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get subscription details by ID
   */
  static async getSubscriptionById(req) {
    const { subscriptionId } = req.params;
    const userId = req.user.id;

    const subscription = await subscriptionModel
      .findOne({ _id: subscriptionId, userId })
      .populate([
        {
          path: 'planId',
          select: 'name slug description isPopular',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price currency paymentType',
        },
        {
          path: 'orderId',
          select: 'razorpay_order_id razorpay_payment_id amount currency status paymentDate',
        },
        {
          path: 'features',
          select: 'name description slug',
        },
      ])
      .lean();

    if (!subscription) {
      throw new NotFoundError('Subscription not found');
    }

    return subscriptionWithPlanSerializer(subscription);
  }

  /**
   * Cancel user's active subscription
   */
  static async cancelSubscription(req) {
    const userId = req.user.id;
    const { reason } = req.body;

    const subscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
    });

    if (!subscription) {
      throw new NotFoundError('No active subscription found');
    }

    // Cancel the subscription
    await subscription.cancel();

    // Update metadata with cancellation reason
    subscription.metadata = {
      ...subscription.metadata,
      cancellationReason: reason,
      cancelledAt: new Date(),
    };
    await subscription.save();

    return {
      success: true,
      message: 'Subscription cancelled successfully',
      subscription: subscriptionSerializer(subscription),
    };
  }

  /**
   * Check and update expired subscriptions (Admin/System use)
   */
  static async updateExpiredSubscriptions() {
    const expiredSubscriptions = await subscriptionModel.find({
      isActive: true,
      endDate: { $lt: new Date() },
    });

    const updatePromises = expiredSubscriptions.map(subscription => subscription.expire());
    await Promise.all(updatePromises);

    return {
      expiredCount: expiredSubscriptions.length,
      expiredSubscriptions: expiredSubscriptions.map(sub => sub._id),
    };
  }

  /**
   * Admin: Get all subscriptions with filters
   */
  static async getAllSubscriptions(req) {
    const {
      page = 1,
      limit = 10,
      status,
      planId,
      userId,
      isActive,
      startDate,
      endDate,
    } = req.query;

    const skip = (page - 1) * limit;
    const filter = {};

    // Apply filters
    if (status) filter.status = status;
    if (planId) filter.planId = planId;
    if (userId) filter.userId = userId;
    if (isActive !== undefined) filter.isActive = isActive === 'true';
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const subscriptions = await subscriptionModel
      .find(filter)
      .populate([
        {
          path: 'userId',
          select: 'email firstName lastName',
        },
        {
          path: 'planId',
          select: 'name slug',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price',
        },
        {
          path: 'orderId',
          select: 'amount status paymentDate',
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await subscriptionModel.countDocuments(filter);

    return {
      subscriptions: subscriptions.map(sub => adminSubscriptionSerializer(sub)),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Admin: Get subscription statistics
   */
  static async getSubscriptionStats() {
    const stats = await subscriptionModel.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
        },
      },
    ]);

    const activeCount = await subscriptionModel.countDocuments({ isActive: true });
    const totalCount = await subscriptionModel.countDocuments();
    const expiredCount = await subscriptionModel.countDocuments({
      endDate: { $lt: new Date() },
      isActive: true,
    });

    return {
      total: totalCount,
      active: activeCount,
      expired: expiredCount,
      statusBreakdown: stats.reduce((acc, stat) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {}),
    };
  }

  /**
   * Admin: Manually activate/deactivate subscription
   */
  static async toggleSubscriptionStatus(req) {
    const { subscriptionId } = req.params;
    const { action } = req.body; // 'activate' or 'deactivate'

    const subscription = await subscriptionModel.findById(subscriptionId);
    if (!subscription) {
      throw new NotFoundError('Subscription not found');
    }

    if (action === 'activate') {
      subscription.status = SubscriptionStatus.ACTIVE;
      subscription.isActive = true;
      if (!subscription.startDate) {
        subscription.startDate = new Date();
      }
    } else if (action === 'deactivate') {
      subscription.status = SubscriptionStatus.CANCELLED;
      subscription.isActive = false;
    } else {
      throw new ValidationError('Invalid action. Use "activate" or "deactivate"');
    }

    await subscription.save();

    return {
      success: true,
      message: `Subscription ${action}d successfully`,
      subscription: subscriptionSerializer(subscription),
    };
  }

  /**
   * Admin: Get subscription by ID (with full details)
   */
  static async getSubscriptionByIdAdmin(req) {
    const { subscriptionId } = req.params;

    const subscription = await subscriptionModel
      .findById(subscriptionId)
      .populate([
        {
          path: 'userId',
          select: 'email firstName lastName phoneNumber',
        },
        {
          path: 'planId',
          select: 'name slug description isPopular',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price currency paymentType',
        },
        {
          path: 'orderId',
          select: 'razorpay_order_id razorpay_payment_id amount currency status paymentDate rawResponse',
        },
        {
          path: 'features',
          select: 'name description slug',
        },
      ])
      .lean();

    if (!subscription) {
      throw new NotFoundError('Subscription not found');
    }

    return adminSubscriptionSerializer(subscription);
  }
}

export default SubscriptionService;
