import mongoose from 'mongoose';
import pricingPlanModel from '../../models/pricing/pricingPlan.modal.js';
import { ValidationMessages } from '../../constants/messages.js';
import { ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import { pricingPlanSerializer } from '../../serializers/pricingPlanSerializer.js';
class PricingPlanService {
  static async createPricingPlan(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { name, description, durations } = req.body;

    if (!name || !description || !durations) {
      throw new ValidationError('Name, description, and durations are required fields');
    }

    if (!Array.isArray(durations)) {
      throw new ValidationError('Durations must be an array');
    }

    durations.forEach((duration) => {
      if (!mongoose.Types.ObjectId.isValid(duration)) {
        throw new ValidationError('Invalid duration ID format');
      }
    });

    const slug = name.toLowerCase().replace(/ /g, '-');

    const newPricingPlan = new pricingPlanModel({
      name,
      description,
      durations,
      slug,
    });

    await newPricingPlan.save();

    return {
      id: newPricingPlan._id,
      name: newPricingPlan.name,
      slug: newPricingPlan.slug,
      description: newPricingPlan.description,
      durations: newPricingPlan.durations,
      isActive: newPricingPlan.isActive,
      isPopular: newPricingPlan.isPopular,
      customSort: newPricingPlan.customSort,
      createdAt: newPricingPlan.createdAt,
      updatedAt: newPricingPlan.updatedAt,
    };
  }

  static async getPricingPlans(req, res) {
    const pricingPlans = await pricingPlanModel
      .find()
      .populate({
        path: 'durations',
        model: 'PlanDurationOption',
        populate: {
          path: 'features',
          model: 'Feature',
        },
      })
      .lean();

    if (!pricingPlans) {
      throw new NotFoundError('Pricing plans not found');
    }

    const formattedPlans = pricingPlans.map((plan) => pricingPlanSerializer(plan));

    return formattedPlans;
  }

  static async getPricingPlanById(req, res) {
    const { id } = req.params;

    const pricingPlan = await pricingPlanModel
      .findById(id)
      .populate({
        path: 'durations',
        model: 'PlanDurationOption',
        populate: {
          path: 'features',
          model: 'Feature',
        },
      })
      .lean();

    if (!pricingPlan) {
      throw new NotFoundError('Pricing plan not found');
    }

    return pricingPlanSerializer(pricingPlan);
  }

  static async updatePricingPlan(req, res) {
    const { id } = req.params;
    const updates = req.body;

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const pricingPlan = await pricingPlanModel.findById(id);

    if (!pricingPlan) {
      throw new NotFoundError('Pricing plan not found');
    }

    Object.assign(pricingPlan, updates);
    await pricingPlan.save();

    return {
      id: pricingPlan._id,
      name: pricingPlan.name,
      slug: pricingPlan.slug,
      description: pricingPlan.description,
      durations: pricingPlan.durations,
      isActive: pricingPlan.isActive,
      isPopular: pricingPlan.isPopular,
      customSort: pricingPlan.customSort,
      createdAt: pricingPlan.createdAt,
      updatedAt: pricingPlan.updatedAt,
    };
  }

  static async deletePricingPlan(req, res) {
    const { id } = req.params;

    const pricingPlan = await pricingPlanModel.findById(id);

    if (!pricingPlan) {
      throw new NotFoundError('Pricing plan not found');
    }

    await pricingPlanModel.findByIdAndDelete(id);

    return null;
  }
}

export default PricingPlanService;
