import mongoose from 'mongoose';
import planPurchaseModel from '../../models/order/order.modal.js';
import subscriptionModel from '../../models/order/subscription.model.js';
import pricingPlanModel from '../../models/pricing/pricingPlan.modal.js';
import planDurationOptionModel from '../../models/pricing/planDurationOption.modal.js';
import couponModel from '../../models/order/coupon.modal.js';
import { ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import { PaymentStatus, SubscriptionStatus } from '../../constants/enums.js';
import {
  createRazorpayOrder,
  verifyPaymentSignature,
  generateReceiptId,
  fetchPaymentDetails,
} from '../../utils/razorpayHelper.js';
import {
  paymentOrderSerializer,
  paymentOrderWithPlanSerializer,
  razorpayOrderSerializer,
  paymentSuccessSerializer,
  paymentHistorySerializer,
} from '../../serializers/paymentSerializer.js';

class PaymentService {
  /**
   * Create a payment order for plan purchase
   */
  static async createPaymentOrder(req) {
    const { planId, durationId, couponCode } = req.body;
    const userId = req.user.id;

    if (!planId || !durationId) {
      throw new ValidationError('Plan ID and Duration ID are required');
    }

    // Validate plan and duration
    const plan = await pricingPlanModel.findById(planId);
    if (!plan || !plan.isActive) {
      throw new NotFoundError('Plan not found or inactive');
    }

    const duration = await planDurationOptionModel.findById(durationId);
    if (!duration || !duration.isActive) {
      throw new NotFoundError('Duration option not found or inactive');
    }

    // Check if duration belongs to the plan
    if (!plan.durations.includes(durationId)) {
      throw new ValidationError('Duration option does not belong to the selected plan');
    }

    // Check for existing active subscription
    const existingSubscription = await subscriptionModel.findOne({
      userId,
      isActive: true,
    });

    if (existingSubscription) {
      throw new ValidationError('User already has an active subscription');
    }

    let finalAmount = duration.price;
    let appliedCoupon = null;

    // Apply coupon if provided
    if (couponCode) {
      const couponResult = await this.applyCoupon(couponCode, userId, finalAmount);
      finalAmount = couponResult.discountedAmount;
      appliedCoupon = couponResult.coupon;
    }

    // Create order in database
    const orderData = {
      userId,
      planId,
      durationId,
      amount: finalAmount,
      currency: duration.currency,
      paymentType: duration.paymentType,
      status: PaymentStatus.CREATED,
      metadata: {
        originalAmount: duration.price,
        appliedCoupon: appliedCoupon ? {
          code: appliedCoupon.code,
          discountType: appliedCoupon.discountType,
          discountValue: appliedCoupon.discountValue,
        } : null,
      },
    };

    // Generate unique receipt ID
    const receipt = generateReceiptId('plan_purchase');

    // Create Razorpay order
    const razorpayOrder = await createRazorpayOrder({
      amount: finalAmount,
      currency: duration.currency,
      receipt,
      notes: {
        userId: userId.toString(),
        planId: planId.toString(),
        durationId: durationId.toString(),
      },
    });

    // Update order with Razorpay order ID
    orderData.razorpay_order_id = razorpayOrder.id;

    const order = new planPurchaseModel(orderData);
    await order.save();

    return razorpayOrderSerializer(razorpayOrder, order);
  }

  /**
   * Verify payment and activate subscription
   */
  static async verifyPayment(req) {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;
    const userId = req.user.id;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      throw new ValidationError('Payment verification data is incomplete');
    }

    // Find the order
    const order = await planPurchaseModel.findOne({
      razorpay_order_id,
      userId,
    }).populate(['planId', 'durationId']);

    if (!order) {
      throw new NotFoundError('Order not found');
    }

    if (order.status === PaymentStatus.SUCCESS) {
      throw new ValidationError('Payment already verified');
    }

    // Verify payment signature
    const isValidSignature = verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
    });

    if (!isValidSignature) {
      // Update order status to failed
      order.status = PaymentStatus.FAILED;
      await order.save();
      throw new ValidationError('Invalid payment signature');
    }

    // Fetch payment details from Razorpay
    const paymentDetails = await fetchPaymentDetails(razorpay_payment_id);

    // Update order with payment details
    order.razorpay_payment_id = razorpay_payment_id;
    order.razorpay_signature = razorpay_signature;
    order.status = PaymentStatus.SUCCESS;
    order.paymentDate = new Date();
    order.rawResponse = paymentDetails;
    await order.save();

    // Create subscription
    const subscription = await this.createSubscription(order);

    // Mark coupon as used if applicable
    if (order.metadata?.appliedCoupon) {
      await this.markCouponAsUsed(order.metadata.appliedCoupon.code, userId);
    }

    return paymentSuccessSerializer(order, subscription);
  }

  /**
   * Create subscription after successful payment
   */
  static async createSubscription(order) {
    const duration = order.durationId;
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + duration.valueInDays * 24 * 60 * 60 * 1000);

    const subscriptionData = {
      userId: order.userId,
      planId: order.planId,
      durationId: order.durationId,
      orderId: order._id,
      status: SubscriptionStatus.ACTIVE,
      startDate,
      endDate,
      isActive: true,
      features: duration.features,
    };

    const subscription = new subscriptionModel(subscriptionData);
    await subscription.save();

    return subscription;
  }

  /**
   * Apply coupon to order
   */
  static async applyCoupon(couponCode, userId, amount) {
    const coupon = await couponModel.findOne({
      code: couponCode,
      isActive: true,
    });

    if (!coupon) {
      throw new NotFoundError('Coupon not found or inactive');
    }

    // Check expiry
    if (coupon.expiresAt && new Date() > coupon.expiresAt) {
      throw new ValidationError('Coupon has expired');
    }

    // Check usage limit
    const userUsage = coupon.usedBy.find(usage => usage.userId.toString() === userId.toString());
    if (userUsage && userUsage.usageCount >= coupon.usageLimitPerUser) {
      throw new ValidationError('Coupon usage limit exceeded');
    }

    // Calculate discount
    let discountAmount = 0;
    if (coupon.discountType === 'percentage') {
      discountAmount = (amount * coupon.discountValue) / 100;
    } else {
      discountAmount = coupon.discountValue;
    }

    const discountedAmount = Math.max(0, amount - discountAmount);

    return {
      coupon,
      discountAmount,
      discountedAmount,
    };
  }

  /**
   * Mark coupon as used
   */
  static async markCouponAsUsed(couponCode, userId) {
    const coupon = await couponModel.findOne({ code: couponCode });
    if (!coupon) return;

    const userUsageIndex = coupon.usedBy.findIndex(
      usage => usage.userId.toString() === userId.toString()
    );

    if (userUsageIndex >= 0) {
      coupon.usedBy[userUsageIndex].usageCount += 1;
      coupon.usedBy[userUsageIndex].usedAt = new Date();
    } else {
      coupon.usedBy.push({
        userId,
        usageCount: 1,
        usedAt: new Date(),
      });
    }

    await coupon.save();
  }

  /**
   * Get user's payment history
   */
  static async getPaymentHistory(req) {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;

    const skip = (page - 1) * limit;

    const orders = await planPurchaseModel
      .find({ userId })
      .populate(['planId', 'durationId'])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await planPurchaseModel.countDocuments({ userId });

    return {
      orders: paymentHistorySerializer(orders),
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get payment details by order ID
   */
  static async getPaymentDetails(req) {
    const { orderId } = req.params;
    const userId = req.user.id;

    const order = await planPurchaseModel
      .findOne({ _id: orderId, userId })
      .populate(['planId', 'durationId'])
      .lean();

    if (!order) {
      throw new NotFoundError('Payment order not found');
    }

    return paymentOrderWithPlanSerializer(order);
  }
}

export default PaymentService;
