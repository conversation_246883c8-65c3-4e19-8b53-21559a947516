import clientModel from '../models/client.modal.js';
import userSettingsModel from '../models/clientsSettings.model.js';
import sleepTrackerModel from '../models/sleepRecord.model.js';
import waterTrackerModel from '../models/waterRecord.model.js';
import stepTrackerModel from '../models/stepRecord.model.js';

export async function setupDefaultUserEnvironment(userId) {
  const today = new Date().toISOString().split('T')[0];

  const [clientProfile, settings, sleep, water, steps] = await Promise.all([
    clientModel.create({ userId }),
    userSettingsModel.create({ userId }),
    sleepTrackerModel.create({ userId, date: today }),
    waterTrackerModel.create({ userId, date: today, consumedLiters: 0 }),
    stepTrackerModel.create({ userId, date: today, steps: 0 }),
  ]);

  return {
    clientProfile,
    settings,
    sleep,
    water,
    steps,
  };
}
