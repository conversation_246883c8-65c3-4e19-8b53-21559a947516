import mongoose from 'mongoose';

const PasswordResetSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    otp: {
      type: String,
      required: true,
    },
    expiresAt: {
      type: Date,
      required: true,
      default: () => new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from creation
    },
    used: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

// TTL index to auto-delete expired documents
PasswordResetSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Other helpful indexes
PasswordResetSchema.index({ otp: 1 });
PasswordResetSchema.index({ userId: 1 });

export default mongoose.models.PasswordReset || mongoose.model('PasswordReset', PasswordResetSchema, 'password_resets');
