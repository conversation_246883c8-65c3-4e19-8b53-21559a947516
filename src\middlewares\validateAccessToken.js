import { TokenConstants } from '../constants/tokenConstants.js';
import { ValidationMessages } from '../constants/messages.js';
import { AppError, ValidationError } from '../utils/errorHandler.js';
import { HttpStatus } from '../constants/httpStatus.js';
import verifyAccessToken from '../utils/verifyAccessToken.js';

/**
 * Middleware to validate the access token.
 * If valid and not expired, proceeds to the next middleware.
 * If invalid, expired, or missing, throws an appropriate error.
 */
const validateAccessToken = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith(`${TokenConstants.BEARER} `)) {
      return next(
        new ValidationError(ValidationMessages.AUTHENTICATION.SESSION_EXPIRED, HttpStatus.STATUS_CODE.UNAUTHORIZED)
      );
    }

    const accessToken = authHeader.split(' ')[1];

    const decoded = verifyAccessToken(accessToken);
    req.user = decoded;

    next();
  } catch (error) {
    if (error instanceof AppError || error instanceof ValidationError) {
      return next(error);
    }

    return next(new AppError('Unexpected error in token validation.', HttpStatus.STATUS_CODE.UNAUTHORIZED));
  }
};

export default validateAccessToken;
