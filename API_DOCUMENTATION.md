# Food for Soul Tech - API Documentation

## 🚀 Base URL
```
http://localhost:8000
```

## 📋 Table of Contents
1. [Authentication](#authentication)
2. [Health Check](#health-check)
3. [Public Routes](#public-routes)
4. [Client Routes](#client-routes)
5. [Admin Routes](#admin-routes)
6. [Static Pages](#static-pages)

---

## 🔐 Authentication

### Headers Required for Protected Routes
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

### User Roles
- `CLIENT`: Regular users who can access client-specific features
- `ADMIN`: Administrators with elevated privileges

---

## 🏥 Health Check

### Basic Health Check
```http
GET /api-health
```
**Access:** Public  
**Description:** Simple health check for monitoring  

**Response:**
```json
{
  "status": "Success",
  "message": "The server is running and healthy."
}
```

### Detailed Health Check
```http
GET /api/health
```
**Access:** Public  
**Description:** Comprehensive health check with system metrics  

**Response:**
```json
{
  "status": "Success",
  "message": "The server is running and healthy.",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-17T02:42:19.494Z",
    "uptime": 54.5752637,
    "environment": "development",
    "version": "v22.13.1",
    "memory": {
      "used": 25.67,
      "total": 35.84,
      "external": 2.15
    },
    "services": {
      "database": "connected",
      "server": "running"
    },
    "system": {
      "platform": "win32",
      "arch": "x64",
      "pid": 12345
    }
  }
}
```

### Extended Health Check
```http
GET /api/health/detailed
```
**Access:** Public  
**Description:** Extended health check with performance metrics  

---

## 🌐 Public Routes

### Get Pricing Plans
```http
GET /api/pricing-plans
```
**Access:** Public  
**Description:** Get all available pricing plans  

**Response:**
```json
{
  "status": "Success",
  "message": "Pricing plans retrieved successfully",
  "data": [
    {
      "id": "68372a398cd0aff33c968ed9",
      "name": "Workout Plan",
      "slug": "workout-plan",
      "description": "Custom workout programming designed...",
      "features": [...],
      "durations": [...]
    }
  ]
}
```

---

## 👤 Client Routes

All client routes require authentication with `CLIENT` role.

### Authentication

#### Register
```http
POST /api/auth/signup
```
**Access:** Public  
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********"
}
```

#### Login
```http
POST /api/auth/login
```
**Access:** Public  
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Forgot Password
```http
POST /api/auth/forgot-password
```
**Access:** Public  
**Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### Reset Password
```http
POST /api/auth/reset-password
```
**Access:** Public  
**Body:**
```json
{
  "token": "reset_token_here",
  "newPassword": "newpassword123"
}
```

#### Change Password
```http
POST /api/auth/change-password
```
**Access:** Protected (CLIENT/ADMIN)  
**Body:**
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

#### Refresh Token
```http
POST /api/auth/refresh-token
```
**Access:** Public (with refresh token)  
**Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

#### Verify Email
```http
POST /api/auth/verify-email
```
**Access:** Public  
**Body:**
```json
{
  "token": "verification_token_here"
}
```

#### Send Verification Email
```http
POST /api/auth/send-verification-email
```
**Access:** Public  
**Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### Logout
```http
POST /api/auth/logout
```
**Access:** Protected (CLIENT/ADMIN)  

### Profile Management

#### Get Client Profile
```http
GET /api/clients/profile/me
```
**Access:** CLIENT only  
**Description:** Get authenticated client's profile  

#### Client Onboarding
```http
POST /api/clients/profile/onboarding
```
**Access:** CLIENT only  
**Description:** Submit onboarding data during first-time setup  
**Body:**
```json
{
  "age": 25,
  "gender": "male",
  "height": 175,
  "weight": 70,
  "activityLevel": "moderate",
  "goals": ["weight_loss", "muscle_gain"]
}
```

#### Update Client Profile
```http
PUT /api/clients/profile/me
```
**Access:** CLIENT only  
**Description:** Update client profile information  
**Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "+**********",
  "age": 26
}
```

#### Get User Settings
```http
GET /api/clients/profile/settings
```
**Access:** CLIENT only  
**Description:** Get client-specific settings and preferences  

#### Update User Settings
```http
PATCH /api/clients/profile/settings
```
**Access:** CLIENT only  
**Description:** Update client settings (partial update)  
**Body:**
```json
{
  "notifications": true,
  "theme": "dark",
  "language": "en"
}
```

### Sleep Tracking

#### Get Sleep Records
```http
GET /api/clients/sleep-tracking
```
**Access:** CLIENT only  
**Description:** Get all sleep records of authenticated client  

#### Get Sleep Records by Date Range
```http
GET /api/clients/sleep-tracking/range?from=2025-01-01&to=2025-01-31
```
**Access:** CLIENT only  
**Description:** Get sleep records within date range  
**Query Parameters:**
- `from`: Start date (YYYY-MM-DD)
- `to`: End date (YYYY-MM-DD)

#### Update Sleep Record
```http
PATCH /api/clients/sleep-tracking
```
**Access:** CLIENT only  
**Description:** Update a sleep record  
**Body:**
```json
{
  "recordId": "sleep_record_id",
  "sleepDuration": 8,
  "sleepQuality": "good",
  "bedTime": "22:00",
  "wakeTime": "06:00"
}
```

### Payments

#### Create Payment Order
```http
POST /api/clients/payments/create-order
```
**Access:** CLIENT only  
**Description:** Create payment order for plan purchase  
**Body:**
```json
{
  "planId": "plan_id_here",
  "durationId": "duration_id_here",
  "couponCode": "OPTIONAL_COUPON"
}
```

#### Verify Payment
```http
POST /api/clients/payments/verify
```
**Access:** CLIENT only  
**Description:** Verify payment and activate subscription  
**Body:**
```json
{
  "razorpay_order_id": "order_id",
  "razorpay_payment_id": "payment_id",
  "razorpay_signature": "signature"
}
```

#### Get Payment History
```http
GET /api/clients/payments/history
```
**Access:** CLIENT only  
**Description:** Get user's payment history  

#### Get Payment Details
```http
GET /api/clients/payments/:id
```
**Access:** CLIENT only  
**Description:** Get payment details by order ID  

### Subscriptions

#### Get Active Subscription
```http
GET /api/clients/subscriptions/active
```
**Access:** CLIENT only  
**Description:** Get user's active subscription  

#### Get Subscription History
```http
GET /api/clients/subscriptions/history?page=1&limit=10
```
**Access:** CLIENT only  
**Description:** Get user's subscription history  
**Query Parameters:**
- `page`: Page number (optional)
- `limit`: Items per page (optional)

#### Get Subscription Details
```http
GET /api/clients/subscriptions/:subscriptionId
```
**Access:** CLIENT only  
**Description:** Get subscription details by ID  

#### Cancel Subscription
```http
POST /api/clients/subscriptions/cancel
```
**Access:** CLIENT only  
**Description:** Cancel user's active subscription  
**Body:**
```json
{
  "reason": "Not satisfied with service"
}
```

### Coupons

#### Validate Coupon
```http
POST /api/clients/coupons/validate
```
**Access:** CLIENT only  
**Description:** Validate a coupon code  
**Body:**
```json
{
  "couponCode": "DISCOUNT20",
  "planId": "plan_id_here",
  "durationId": "duration_id_here"
}
```

---

## 🔧 Admin Routes

All admin routes require authentication with `ADMIN` role.

### Authentication

#### Admin Login
```http
POST /api/auth/login/admin
```
**Access:** Public  
**Description:** Admin login with elevated privileges  
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "adminpassword123"
}
```

### Dashboard

#### Get Dashboard Overview
```http
GET /api/admin/dashboard
```
**Access:** ADMIN only  
**Description:** Get comprehensive dashboard analytics  

#### Get Dashboard Stats
```http
GET /api/admin/dashboard/stats
```
**Access:** ADMIN only  
**Description:** Get detailed statistics for admin dashboard  

### Client Management

#### Create Client (Admin)
```http
POST /api/admin/clients
```
**Access:** ADMIN only  
**Description:** Create new client account  
**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "Jane",
  "lastName": "Smith",
  "phoneNumber": "+**********"
}
```

#### Get All Clients
```http
GET /api/admin/clients?page=1&limit=10
```
**Access:** ADMIN only  
**Description:** Get paginated list of all clients  
**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `search`: Search term
- `status`: Filter by status

#### Get Client by ID
```http
GET /api/admin/clients/:id
```
**Access:** ADMIN only  
**Description:** Get detailed client information  

#### Update Client (Admin)
```http
PUT /api/admin/clients/:id
```
**Access:** ADMIN only  
**Description:** Update client information  

#### Activate Client
```http
PATCH /api/admin/clients/:id/activate
```
**Access:** ADMIN only  
**Description:** Activate client account  

#### Deactivate Client
```http
PATCH /api/admin/clients/:id/deactivate
```
**Access:** ADMIN only  
**Description:** Deactivate client account  

#### Delete Client
```http
DELETE /api/admin/clients/:id
```
**Access:** ADMIN only  
**Description:** Permanently delete client account  

#### Get Client Sleep Record
```http
GET /api/admin/clients/:id/sleep-records/:recordId
```
**Access:** ADMIN only  
**Description:** Get specific sleep record for any client  

### Payment Management

#### Get All Payments
```http
GET /api/admin/payments?page=1&limit=10&status=success
```
**Access:** ADMIN only  
**Description:** Get all payment orders with filters  
**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Payment status
- `userId`: Filter by user ID
- `planId`: Filter by plan ID
- `startDate`: Start date filter
- `endDate`: End date filter

#### Get Payment Statistics
```http
GET /api/admin/payments/stats
```
**Access:** ADMIN only  
**Description:** Get payment statistics and revenue data  

#### Get Payment Details (Admin)
```http
GET /api/admin/payments/:id
```
**Access:** ADMIN only  
**Description:** Get detailed payment information  

### Subscription Management

#### Get All Subscriptions
```http
GET /api/admin/subscriptions?page=1&limit=10
```
**Access:** ADMIN only  
**Description:** Get all subscriptions with filters  
**Query Parameters:**
- `page`: Page number
- `limit`: Items per page
- `status`: Subscription status
- `planId`: Filter by plan ID
- `userId`: Filter by user ID
- `isActive`: Filter by active status
- `startDate`: Start date filter
- `endDate`: End date filter

#### Get Subscription Statistics
```http
GET /api/admin/subscriptions/stats
```
**Access:** ADMIN only  
**Description:** Get subscription statistics  

#### Update Expired Subscriptions
```http
POST /api/admin/subscriptions/update-expired
```
**Access:** ADMIN only  
**Description:** Batch update expired subscriptions  

#### Get Subscription Details (Admin)
```http
GET /api/admin/subscriptions/:id
```
**Access:** ADMIN only  
**Description:** Get subscription details by ID  

#### Toggle Subscription Status
```http
PATCH /api/admin/subscriptions/:id/toggle
```
**Access:** ADMIN only  
**Description:** Activate/deactivate subscription  
**Body:**
```json
{
  "action": "activate"
}
```

### Coupon Management

#### Create Coupon
```http
POST /api/admin/coupons
```
**Access:** ADMIN only  
**Description:** Create new coupon  
**Body:**
```json
{
  "code": "DISCOUNT20",
  "discountType": "percentage",
  "discountValue": 20,
  "maxUses": 100,
  "validFrom": "2025-01-01",
  "validUntil": "2025-12-31",
  "applicablePlans": ["plan_id_1", "plan_id_2"]
}
```

#### Get All Coupons
```http
GET /api/admin/coupons?page=1&limit=10
```
**Access:** ADMIN only  
**Description:** Get all coupons with pagination  

#### Get Coupon by ID
```http
GET /api/admin/coupons/:id
```
**Access:** ADMIN only  
**Description:** Get specific coupon details  

#### Update Coupon
```http
PUT /api/admin/coupons/:id
```
**Access:** ADMIN only  
**Description:** Update coupon information  

#### Activate Coupon
```http
PATCH /api/admin/coupons/:id/activate
```
**Access:** ADMIN only  
**Description:** Activate a coupon  

#### Deactivate Coupon
```http
PATCH /api/admin/coupons/:id/deactivate
```
**Access:** ADMIN only  
**Description:** Deactivate a coupon  

#### Delete Coupon
```http
DELETE /api/admin/coupons/:id
```
**Access:** ADMIN only  
**Description:** Delete a coupon  

### Pricing Management

#### Plan Features

##### Create Plan Feature
```http
POST /api/admin/pricing/features
```
**Access:** ADMIN only  
**Body:**
```json
{
  "name": "Premium Support",
  "description": "24/7 customer support",
  "slug": "premium-support"
}
```

##### Get All Plan Features
```http
GET /api/admin/pricing/features
```
**Access:** ADMIN only  

##### Get Plan Feature by ID
```http
GET /api/admin/pricing/features/:id
```
**Access:** ADMIN only  

##### Update Plan Feature
```http
PUT /api/admin/pricing/features/:id
```
**Access:** ADMIN only  

##### Delete Plan Feature
```http
DELETE /api/admin/pricing/features/:id
```
**Access:** ADMIN only  

#### Plan Duration Options

##### Create Duration Option
```http
POST /api/admin/pricing/durations
```
**Access:** ADMIN only  
**Body:**
```json
{
  "label": "6 Months",
  "valueInDays": 180,
  "price": 299,
  "currency": "INR",
  "paymentType": "one_time",
  "features": ["feature_id_1", "feature_id_2"]
}
```

##### Get All Duration Options
```http
GET /api/admin/pricing/durations
```
**Access:** ADMIN only  

##### Get Duration Option by ID
```http
GET /api/admin/pricing/durations/:id
```
**Access:** ADMIN only  

##### Update Duration Option
```http
PUT /api/admin/pricing/durations/:id
```
**Access:** ADMIN only  

##### Delete Duration Option
```http
DELETE /api/admin/pricing/durations/:id
```
**Access:** ADMIN only  

#### Pricing Plans

##### Create Pricing Plan
```http
POST /api/admin/pricing/plans
```
**Access:** ADMIN only  
**Body:**
```json
{
  "name": "Premium Plan",
  "slug": "premium-plan",
  "description": "Complete fitness solution",
  "features": ["feature_id_1", "feature_id_2"],
  "durations": ["duration_id_1", "duration_id_2"],
  "isActive": true
}
```

##### Get All Pricing Plans (Admin)
```http
GET /api/admin/pricing/plans
```
**Access:** ADMIN only  

##### Get Pricing Plan by ID
```http
GET /api/admin/pricing/plans/:id
```
**Access:** ADMIN only  

##### Update Pricing Plan
```http
PUT /api/admin/pricing/plans/:id
```
**Access:** ADMIN only  

##### Delete Pricing Plan
```http
DELETE /api/admin/pricing/plans/:id
```
**Access:** ADMIN only  

---

## 📄 Static Pages

### Home Page
```http
GET /
```
**Access:** Public  
**Description:** Main landing page  

### Health Dashboard
```http
GET /health
```
**Access:** Public  
**Description:** Interactive health monitoring dashboard  

### Health Test Interface
```http
GET /test-health
```
**Access:** Public  
**Description:** Health check testing interface  

### Documentation
```http
GET /docs
```
**Access:** Public  
**Description:** API documentation page  

### Status Page (Redirect)
```http
GET /status
```
**Access:** Public  
**Description:** Redirects to /health  

### Monitor Page (Redirect)
```http
GET /monitor
```
**Access:** Public  
**Description:** Redirects to /health  

---

## 📝 Common Response Format

### Success Response
```json
{
  "status": "Success",
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "status": "Error",
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
```

### Pagination Response
```json
{
  "status": "Success",
  "message": "Data retrieved successfully",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "pages": 10
    }
  }
}
```

---

## 🔒 HTTP Status Codes

- `200` - OK
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `500` - Internal Server Error

---

## 🧪 Testing with Postman

1. **Import Environment**: Create a new environment with base URL
2. **Set Variables**: 
   - `baseUrl`: `http://localhost:8000`
   - `accessToken`: Your JWT token after login
3. **Authentication**: Add `Authorization: Bearer {{accessToken}}` to protected routes
4. **Content-Type**: Set to `application/json` for POST/PUT requests

### Sample Postman Collection Structure
```
Food for Soul Tech API
├── Health Check
│   ├── Basic Health Check
│   ├── Detailed Health Check
│   └── Extended Health Check
├── Authentication
│   ├── Register
│   ├── Login
│   ├── Admin Login
│   ├── Forgot Password
│   ├── Reset Password
│   ├── Change Password
│   ├── Refresh Token
│   ├── Verify Email
│   └── Logout
├── Public
│   └── Get Pricing Plans
├── Client
│   ├── Profile Management
│   ├── Sleep Tracking
│   ├── Payments
│   ├── Subscriptions
│   └── Coupons
└── Admin
    ├── Dashboard
    ├── Client Management
    ├── Payment Management
    ├── Subscription Management
    ├── Coupon Management
    └── Pricing Management
```

---

## 📞 Support

For API support and questions, please contact the development team.

**Server Status**: ✅ Running on http://localhost:8000  
**Last Updated**: 2025-06-17  
**API Version**: 1.0.0
