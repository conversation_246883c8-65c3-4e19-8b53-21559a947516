import { ValidationMessages } from '../constants/messages.js';
import { AppError, ValidationError, NotFoundError } from '../utils/errorHandler.js';

import clientModel from '../models/client.modal.js';
import userModel from '../models/user.model.js';
import userSettingsModel from '../models/clientsSettings.model.js';
import sleepTrackerModel from '../models/sleepRecord.model.js';
import waterTrackerModel from '../models/waterRecord.model.js';
import stepTrackerModel from '../models/stepRecord.model.js';
import { setupDefaultUserEnvironment } from '../utils/userSetup.js';

import { HttpStatus } from '../constants/httpStatus.js';
import { UserRoles } from '../constants/userRoles.js';
import userSerializer from '../serializers/userSerializer.js';
import clientSerializer from '../serializers/clientSerializer.js';
import mongoose from 'mongoose';

class ClientService {
  static async createClientByAdmin(req, res) {
    const { first_name, last_name, email, password, phoneNumber, password_confirmation } = req.body;

    if (!first_name || !last_name || !email || !password || !password_confirmation) {
      throw new ValidationError('Missing required fields');
    }

    if (password !== password_confirmation) {
      throw new ValidationError('Password and password confirmation do not match');
    }

    // Check if email already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      throw new ValidationError('Email already registered');
    }

    const newUser = new userModel({
      email,
      firstName: first_name,
      lastName: last_name,
      phoneNumber,
      password,
    });

    await newUser.save();

    const { clientProfile, userSettings, sleep, water, steps } = await setupDefaultUserEnvironment(newUser._id);

    newUser.profile = clientProfile._id;
    newUser.isVerified = true;
    await newUser.save();

    return userSerializer(newUser, { allow: ['profile'] });
  }

  static async onboardingProcess(req, res) {
    const userIdFromReq = req.user;

    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const {
      height,
      weight,
      current_weight,
      expected_weight,
      address,
      eating_preference,
      intoxication,
      medical_issues,
      stress_level,
      eating_habits,
      description,
      goals,
    } = req.body;

    // Field-Level Validations
    if (!height || !weight || !current_weight || !expected_weight) {
      throw new ValidationError('Missing required client data (height, weight, current_weight, expected_weight)');
    }

    if (!address || typeof address !== 'string') {
      throw new ValidationError('Address is required and must be a string');
    }

    if (!eating_preference || !['Vegetarian', 'Non-Vegetarian', 'Vegan'].includes(eating_preference)) {
      throw new ValidationError('Invalid or missing eating preference');
    }

    if (!Array.isArray(intoxication) || !Array.isArray(medical_issues)) {
      throw new ValidationError('Intoxication and medical_issues must be arrays');
    }

    if (typeof stress_level !== 'number' || stress_level < 0 || stress_level > 10) {
      throw new ValidationError('Stress level must be a number between 0 and 10');
    }

    if (
      !eating_habits ||
      typeof eating_habits.breakfast !== 'string' ||
      typeof eating_habits.lunch !== 'string' ||
      typeof eating_habits.dinner !== 'string' ||
      typeof eating_habits.snacks !== 'string' ||
      !eating_habits.breakfast.trim() ||
      !eating_habits.lunch.trim() ||
      !eating_habits.dinner.trim() ||
      !eating_habits.snacks.trim()
    ) {
      throw new ValidationError('Eating habits must include non-empty breakfast, lunch, dinner, and snacks as strings');
    }

    // User Validations
    const user = await userModel.findById(userIdFromReq);
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INSUFFICIENT_PERMISSIONS, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    if (user.isOnboarded) {
      throw new ValidationError('Client onboarding already completed');
    }

    if (!user.isVerified) {
      throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_VERIFIED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    if (user.isDeactivated) {
      throw new AppError(ValidationMessages.AUTHENTICATION.DEACTIVATED, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Validate numeric values
    if (height <= 0 || weight <= 0 || current_weight <= 0 || expected_weight <= 0) {
      throw new ValidationError('Height, weight, current weight, and expected weight must be positive numbers');
    }

    const updatedClientResponse = await clientModel.findOneAndUpdate(
      { userId: userIdFromReq },
      {
        height,
        weight,
        current_weight,
        expected_weight,
        address: address.trim(),
        eating_preference,
        intoxication: intoxication.filter((item) => item && item.trim()),
        medical_issues: medical_issues.filter((item) => item && item.trim()),
        stress_level,
        eating_habits: {
          breakfast: eating_habits.breakfast.trim(),
          lunch: eating_habits.lunch.trim(),
          dinner: eating_habits.dinner.trim(),
          snacks: eating_habits.snacks.trim(),
        },
        description: description?.trim(),
        goals: goals?.filter((goal) => goal && goal.trim()),
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true,
        runValidators: true,
      }
    );

    user.isOnboarded = true;
    await user.save();
    return clientSerializer(updatedClientResponse);
  }

  static async getClientProfile(req, res) {
    const userIdFromReq = req.user;

    // Fetch user with complete profile information
    const user = await userModel.findById(userIdFromReq).populate({
      path: 'profile',
      // populate: [
      //   {
      //     path: 'coachId',
      //     select: 'firstName lastName email phoneNumber',
      //   },
      //   {
      //     path: 'fitnessPlan',
      //     select: 'name description duration exercises goals',
      //   },
      //   {
      //     path: 'workoutHistory',
      //     select: 'date exercises duration feedback',
      //   },
      //   {
      //     path: 'nutrition',
      //     select: 'mealPlan dietaryRestrictions supplements recommendations',
      //   },
      // ],
    });

    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    if (!user.isOnboarded) {
      throw new ValidationError('Client onboarding not completed');
    }

    if (!user.isVerified) {
      throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_VERIFIED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    if (user.isDeactivated) {
      throw new AppError(ValidationMessages.AUTHENTICATION.DEACTIVATED, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    if (!user.profile) {
      throw new NotFoundError('Client profile not found');
    }

    // Calculate progress metrics if available
    const progress = {
      weightProgress: user.profile.current_weight - user.profile.weight,
      goalProgress: user.profile.goals?.map((goal) => ({
        goal,
        status: 'in_progress', // This could be dynamic based on actual tracking
      })),

      workoutCompletion: user.profile.workoutHistory?.length || 0,
      nutritionAdherence: 85, // This could be calculated based on actual tracking
    };

    // Combine user and profile data with progress
    const enrichedProfile = {
      ...userSerializer(user, { allow: ['profile'] }),
      progress,
    };

    return enrichedProfile;
  }

  static async updateClientDetails(req, res) {
    const userId = req.user;

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      dateOfBirth,
      height,
      weight,
      current_weight,
      expected_weight,
      address,
      eating_preference,
      intoxication,
      medical_issues,
      stress_level,
      eating_habits,
      description,
      goals,
      coachId,
      fitnessPlan,
      workoutHistory,
      nutrition,
    } = req.body;

    // User Checks
    const user = await userModel.findById(userId);
    if (!user) {
      throw new NotFoundError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_FOUND);
    }

    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError(ValidationMessages.AUTHENTICATION.INSUFFICIENT_PERMISSIONS, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // User should be onboarded
    if (!user.isOnboarded) {
      throw new ValidationError('Client must complete onboarding before updating details');
    }

    if (!user.isVerified) {
      throw new AppError(ValidationMessages.AUTHENTICATION.ACCOUNT_NOT_VERIFIED, HttpStatus.STATUS_CODE.UNAUTHORIZED);
    }

    if (user.isDeactivated) {
      throw new AppError(ValidationMessages.AUTHENTICATION.DEACTIVATED, HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Ensure Client Profile Exists
    const existingClient = await clientModel.findOne({ userId });
    if (!existingClient) {
      throw new NotFoundError('Client profile does not exist. Cannot update.');
    }

    // Prepare update object with only provided fields
    const clientUpdates = {};

    // Validate and add numeric fields if provided
    if (height !== undefined) {
      if (typeof height !== 'number' || height <= 0) {
        throw new ValidationError('Height must be a positive number');
      }
      clientUpdates.height = height;
    }

    if (weight !== undefined) {
      if (typeof weight !== 'number' || weight <= 0) {
        throw new ValidationError('Weight must be a positive number');
      }
      clientUpdates.weight = weight;
    }

    if (current_weight !== undefined) {
      if (typeof current_weight !== 'number' || current_weight <= 0) {
        throw new ValidationError('Current weight must be a positive number');
      }
      clientUpdates.current_weight = current_weight;
    }

    if (expected_weight !== undefined) {
      if (typeof expected_weight !== 'number' || expected_weight <= 0) {
        throw new ValidationError('Expected weight must be a positive number');
      }
      clientUpdates.expected_weight = expected_weight;
    }

    // Validate and add string fields if provided
    if (address !== undefined) {
      if (typeof address !== 'string' || !address.trim()) {
        throw new ValidationError('Address must be a non-empty string');
      }
      clientUpdates.address = address.trim();
    }

    if (eating_preference !== undefined) {
      if (!['Vegetarian', 'Non-Vegetarian', 'Vegan'].includes(eating_preference)) {
        throw new ValidationError('Invalid eating preference. Must be Vegetarian, Non-Vegetarian, or Vegan');
      }
      clientUpdates.eating_preference = eating_preference;
    }

    // Validate and add array fields if provided
    if (intoxication !== undefined) {
      if (!Array.isArray(intoxication)) {
        throw new ValidationError('Intoxication must be an array');
      }
      clientUpdates.intoxication = intoxication.filter((item) => item && typeof item === 'string' && item.trim());
    }

    if (medical_issues !== undefined) {
      if (!Array.isArray(medical_issues)) {
        throw new ValidationError('Medical issues must be an array');
      }
      clientUpdates.medical_issues = medical_issues.filter((item) => item && typeof item === 'string' && item.trim());
    }

    if (stress_level !== undefined) {
      if (typeof stress_level !== 'number' || stress_level < 0 || stress_level > 10) {
        throw new ValidationError('Stress level must be a number between 0 and 10');
      }
      clientUpdates.stress_level = stress_level;
    }

    // Validate eating habits if provided
    if (eating_habits !== undefined) {
      if (
        !eating_habits ||
        typeof eating_habits !== 'object' ||
        (eating_habits.breakfast !== undefined &&
          (typeof eating_habits.breakfast !== 'string' || !eating_habits.breakfast.trim())) ||
        (eating_habits.lunch !== undefined &&
          (typeof eating_habits.lunch !== 'string' || !eating_habits.lunch.trim())) ||
        (eating_habits.dinner !== undefined &&
          (typeof eating_habits.dinner !== 'string' || !eating_habits.dinner.trim())) ||
        (eating_habits.snacks !== undefined &&
          (typeof eating_habits.snacks !== 'string' || !eating_habits.snacks.trim()))
      ) {
        throw new ValidationError(
          'Eating habits must be an object with valid string values for breakfast, lunch, dinner, and snacks'
        );
      }

      const updatedEatingHabits = { ...existingClient.eating_habits };
      if (eating_habits.breakfast !== undefined) updatedEatingHabits.breakfast = eating_habits.breakfast.trim();
      if (eating_habits.lunch !== undefined) updatedEatingHabits.lunch = eating_habits.lunch.trim();
      if (eating_habits.dinner !== undefined) updatedEatingHabits.dinner = eating_habits.dinner.trim();
      if (eating_habits.snacks !== undefined) updatedEatingHabits.snacks = eating_habits.snacks.trim();

      clientUpdates.eating_habits = updatedEatingHabits;
    }

    // Add optional fields if provided
    if (description !== undefined) {
      clientUpdates.description = typeof description === 'string' ? description.trim() : description;
    }

    if (goals !== undefined) {
      if (!Array.isArray(goals)) {
        throw new ValidationError('Goals must be an array');
      }
      clientUpdates.goals = goals.filter((goal) => goal && typeof goal === 'string' && goal.trim());
    }

    // Add reference fields if provided and valid
    if (coachId !== undefined) {
      if (coachId && !mongoose.Types.ObjectId.isValid(coachId)) {
        throw new ValidationError('Invalid coach ID format');
      }
      clientUpdates.coachId = coachId;
    }

    if (fitnessPlan !== undefined) {
      if (fitnessPlan && !mongoose.Types.ObjectId.isValid(fitnessPlan)) {
        throw new ValidationError('Invalid fitness plan ID format');
      }
      clientUpdates.fitnessPlan = fitnessPlan;
    }

    if (nutrition !== undefined) {
      if (typeof nutrition === 'string') {
        // It's an ObjectId string
        if (nutrition && !mongoose.Types.ObjectId.isValid(nutrition)) {
          throw new ValidationError('Invalid nutrition plan ID format');
        }
        clientUpdates.nutrition = nutrition;
      } else if (nutrition && typeof nutrition === 'object') {
        // It's a nutrition object - this should be handled by creating a NutritionPlan first
        throw new ValidationError(
          'Nutrition should be an ObjectId reference to a NutritionPlan document. Please create a NutritionPlan document first.'
        );
      } else if (nutrition === null) {
        // Allow setting to null
        clientUpdates.nutrition = null;
      } else {
        throw new ValidationError('Nutrition must be a valid ObjectId string or null');
      }
    }

    if (workoutHistory !== undefined) {
      if (!Array.isArray(workoutHistory)) {
        throw new ValidationError('Workout history must be an array');
      }

      // Handle both ObjectId strings and workout objects
      const processedWorkoutHistory = [];
      for (const workout of workoutHistory) {
        if (typeof workout === 'string') {
          // It's an ObjectId string
          if (!mongoose.Types.ObjectId.isValid(workout)) {
            throw new ValidationError('Invalid workout history ID format');
          }
          processedWorkoutHistory.push(workout);
        } else if (workout && typeof workout === 'object') {
          // It's a workout object - this should be handled by creating a WorkoutSession first
          // For now, we'll skip objects and only accept ObjectId references
          throw new ValidationError(
            'Workout history should contain ObjectId references to WorkoutSession documents. Please create WorkoutSession documents first.'
          );
        }
      }
      clientUpdates.workoutHistory = processedWorkoutHistory;
    }

    // Perform the Update
    Object.assign(existingClient, clientUpdates);
    await existingClient.save();

    // Update user fields if provided
    const userUpdates = {};
    if (firstName !== undefined) userUpdates.firstName = firstName;
    if (lastName !== undefined) userUpdates.lastName = lastName;
    if (phoneNumber !== undefined) userUpdates.phoneNumber = phoneNumber;
    if (dateOfBirth !== undefined) userUpdates.dateOfBirth = dateOfBirth;

    if (Object.keys(userUpdates).length > 0) {
      Object.assign(user, userUpdates);
      await user.save();
    }

    // Return serialized response
    const updatedUser = await userModel.findById(userId).populate('profile');
    return userSerializer(updatedUser, { allow: ['profile'] });
  }

  static async getClientsList(req, res) {
    const user = await userModel.find({ roles: UserRoles.CLIENT }).populate({
      path: 'profile',
      // populate: [
      //   {
      //     path: 'coachId',
      //     select: 'firstName lastName email phoneNumber',
      //   },
      //   {
      //     path: 'fitnessPlan',
      //     select: 'name description duration exercises goals',
      //   },
      //   {
      //     path: 'workoutHistory',
      //     select: 'date exercises duration feedback',
      //   },
      //   {
      //     path: 'nutrition',
      //     select: 'mealPlan dietaryRestrictions supplements recommendations',
      //   },
      // ],
    });

    // Serialize each user in the data array
    const formattedClients = user.map((user) =>
      userSerializer(user, {
        allow: ['isOnboarded', 'isVerified', 'isDeactivated', 'deactivationReason', 'deactivatedAt'],
      })
    );

    return formattedClients;
  }

  static async getClientById(req, res) {
    const { id } = req.params;

    // Validate the ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new ValidationError('Invalid client ID format');
    }

    // Find user by ID and populate profile
    const user = await userModel.findById(id).populate({
      path: 'profile',
      // populate: [
      //   {
      //     path: 'coachId',
      //     select: 'firstName lastName email phoneNumber',
      //   },
      //   {
      //     path: 'fitnessPlan',
      //     select: 'name description duration exercises goals',
      //   },
      //   {
      //     path: 'workoutHistory',
      //     select: 'date exercises duration feedback',
      //   },
      //   {
      //     path: 'nutrition',
      //     select: 'mealPlan dietaryRestrictions supplements recommendations',
      //   },
      // ],
    });

    if (!user) {
      throw new NotFoundError(`User not found with the ID: ${id}`);
    }

    // Verify the user is a client
    if (user.roles !== UserRoles.CLIENT) {
      throw new ValidationError('The specified user is not a client');
    }

    // Check if client profile exists
    if (!user.profile) {
      throw new NotFoundError('Client profile not found. User may not have completed onboarding.');
    }

    // Return serialized user with profile
    return userSerializer(user, {
      allow: ['profile', 'isOnboarded', 'isVerified', 'isDeactivated', 'deactivationReason', 'deactivatedAt'],
    });
  }

  static async updateClientByAdmin(req, res) {
    const { id } = req.params; // Admin sends client user ID as param
    const updates = req.body;

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    // Find the user
    const user = await userModel.findById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError('Target user is not a client', HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Find the client profile
    const client = await clientModel.findOne({ userId: id });
    if (!client) {
      throw new NotFoundError('Client profile not found');
    }

    // Extract fields for validation and updates
    const {
      firstName,
      lastName,
      phoneNumber,
      dateOfBirth,
      avatar,
      height,
      weight,
      current_weight,
      expected_weight,
      address,
      eating_preference,
      intoxication,
      medical_issues,
      stress_level,
      eating_habits,
      description,
      goals,
      coachId,
      fitnessPlan,
      workoutHistory,
      nutrition,
    } = updates;

    // Validate client-specific fields if provided (similar to updateClientDetails)
    const clientUpdates = {};

    if (height !== undefined) {
      if (typeof height !== 'number' || height <= 0) {
        throw new ValidationError('Height must be a positive number');
      }
      clientUpdates.height = height;
    }

    if (weight !== undefined) {
      if (typeof weight !== 'number' || weight <= 0) {
        throw new ValidationError('Weight must be a positive number');
      }
      clientUpdates.weight = weight;
    }

    if (current_weight !== undefined) {
      if (typeof current_weight !== 'number' || current_weight <= 0) {
        throw new ValidationError('Current weight must be a positive number');
      }
      clientUpdates.current_weight = current_weight;
    }

    if (expected_weight !== undefined) {
      if (typeof expected_weight !== 'number' || expected_weight <= 0) {
        throw new ValidationError('Expected weight must be a positive number');
      }
      clientUpdates.expected_weight = expected_weight;
    }

    if (address !== undefined) {
      if (typeof address !== 'string' || !address.trim()) {
        throw new ValidationError('Address must be a non-empty string');
      }
      clientUpdates.address = address.trim();
    }

    if (eating_preference !== undefined) {
      if (!['Vegetarian', 'Non-Vegetarian', 'Vegan'].includes(eating_preference)) {
        throw new ValidationError('Invalid eating preference. Must be Vegetarian, Non-Vegetarian, or Vegan');
      }
      clientUpdates.eating_preference = eating_preference;
    }

    if (intoxication !== undefined) {
      if (!Array.isArray(intoxication)) {
        throw new ValidationError('Intoxication must be an array');
      }
      clientUpdates.intoxication = intoxication.filter((item) => item && typeof item === 'string' && item.trim());
    }

    if (medical_issues !== undefined) {
      if (!Array.isArray(medical_issues)) {
        throw new ValidationError('Medical issues must be an array');
      }
      clientUpdates.medical_issues = medical_issues.filter((item) => item && typeof item === 'string' && item.trim());
    }

    if (stress_level !== undefined) {
      if (typeof stress_level !== 'number' || stress_level < 0 || stress_level > 10) {
        throw new ValidationError('Stress level must be a number between 0 and 10');
      }
      clientUpdates.stress_level = stress_level;
    }

    if (eating_habits !== undefined) {
      if (!eating_habits || typeof eating_habits !== 'object') {
        throw new ValidationError('Eating habits must be an object');
      }
      const updatedEatingHabits = { ...client.eating_habits };
      if (eating_habits.breakfast !== undefined) updatedEatingHabits.breakfast = eating_habits.breakfast;
      if (eating_habits.lunch !== undefined) updatedEatingHabits.lunch = eating_habits.lunch;
      if (eating_habits.dinner !== undefined) updatedEatingHabits.dinner = eating_habits.dinner;
      if (eating_habits.snacks !== undefined) updatedEatingHabits.snacks = eating_habits.snacks;
      clientUpdates.eating_habits = updatedEatingHabits;
    }

    if (description !== undefined) {
      clientUpdates.description = typeof description === 'string' ? description.trim() : description;
    }

    if (goals !== undefined) {
      if (!Array.isArray(goals)) {
        throw new ValidationError('Goals must be an array');
      }
      clientUpdates.goals = goals.filter((goal) => goal && typeof goal === 'string' && goal.trim());
    }

    // Validate reference fields
    if (coachId !== undefined) {
      if (coachId && !mongoose.Types.ObjectId.isValid(coachId)) {
        throw new ValidationError('Invalid coach ID format');
      }
      clientUpdates.coachId = coachId;
    }

    if (fitnessPlan !== undefined) {
      if (fitnessPlan && !mongoose.Types.ObjectId.isValid(fitnessPlan)) {
        throw new ValidationError('Invalid fitness plan ID format');
      }
      clientUpdates.fitnessPlan = fitnessPlan;
    }

    if (nutrition !== undefined) {
      if (typeof nutrition === 'string') {
        if (nutrition && !mongoose.Types.ObjectId.isValid(nutrition)) {
          throw new ValidationError('Invalid nutrition plan ID format');
        }
        clientUpdates.nutrition = nutrition;
      } else if (nutrition === null) {
        clientUpdates.nutrition = null;
      } else {
        throw new ValidationError('Nutrition must be a valid ObjectId string or null');
      }
    }

    if (workoutHistory !== undefined) {
      if (!Array.isArray(workoutHistory)) {
        throw new ValidationError('Workout history must be an array');
      }
      for (const workoutId of workoutHistory) {
        if (workoutId && !mongoose.Types.ObjectId.isValid(workoutId)) {
          throw new ValidationError('Invalid workout history ID format');
        }
      }
      clientUpdates.workoutHistory = workoutHistory;
    }

    // Update client profile
    if (Object.keys(clientUpdates).length > 0) {
      Object.assign(client, clientUpdates);
      await client.save();
    }

    // Update user basic info
    const userUpdates = {};
    if (firstName !== undefined) userUpdates.firstName = firstName;
    if (lastName !== undefined) userUpdates.lastName = lastName;
    if (phoneNumber !== undefined) userUpdates.phoneNumber = phoneNumber;
    if (dateOfBirth !== undefined) userUpdates.dateOfBirth = dateOfBirth;
    if (avatar !== undefined) userUpdates.avatar = avatar;

    if (Object.keys(userUpdates).length > 0) {
      Object.assign(user, userUpdates);
      await user.save();
    }

    // Return updated user with profile
    const updatedUser = await userModel.findById(id).populate('profile');
    return userSerializer(updatedUser, { allow: ['profile'] });
  }

  static async deactivateClientByAdmin(req, res) {
    const { id } = req.params;
    const { deactivationReason } = req.body;

    // Find the user with populated profile
    const user = await userModel.findById(id).populate('profile');
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify the user is a client
    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError('Target user is not a client', HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Deactivate the user
    user.isDeactivated = true;
    user.deactivationReason = deactivationReason || 'No reason provided';
    user.deactivatedAt = new Date();
    await user.save();

    return userSerializer(user, {
      allow: ['isDeactivated', 'deactivationReason', 'deactivatedAt'],
    });
  }

  static async activateClientByAdmin(req, res) {
    const { id } = req.params;

    // Find the user with populated profile
    const user = await userModel.findById(id).populate('profile');
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify the user is a client
    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError('Target user is not a client', HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Activate the user
    user.isDeactivated = false;
    user.deactivationReason = null;
    user.deactivatedAt = null;
    await user.save();

    return userSerializer(user, {
      allow: ['isDeactivated', 'deactivationReason', 'deactivatedAt'],
    });
  }

  static async deleteClientByAdmin(req, res) {
    const { id } = req.params;

    // Find the user with populated profile
    const user = await userModel.findById(id).populate('profile');
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Verify the user is a client
    if (user.roles !== UserRoles.CLIENT) {
      throw new AppError('Target user is not a client', HttpStatus.STATUS_CODE.FORBIDDEN);
    }

    // Find the client profile
    const client = await clientModel.findOne({ userId: id });
    if (!client) {
      throw new NotFoundError('Client profile not found');
    }

    await clientModel.findOneAndDelete({ userId: id });
    await userModel.findOneAndDelete({ _id: id });
    await userSettingsModel.findOneAndDelete({ userId: id });
    await sleepTrackerModel.findOneAndDelete({ userId: id });
    await waterTrackerModel.findOneAndDelete({ userId: id });
    await stepTrackerModel.findOneAndDelete({ userId: id });

    return null;
  }
}

export default ClientService;
