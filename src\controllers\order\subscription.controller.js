import SubscriptionService from '../../services/order/subscription.service.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';

/**
 * Get user's active subscription
 * @route GET /api/subscriptions/active
 * @access Client
 */
const getActiveSubscription = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getActiveSubscription(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Active subscription retrieved successfully',
    data: subscriptionData,
  });
});

/**
 * Get user's subscription history
 * @route GET /api/subscriptions/history
 * @access Client
 */
const getSubscriptionHistory = asyncHandler(async (req, res) => {
  const historyData = await SubscriptionService.getSubscriptionHistory(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Subscription history retrieved successfully',
    data: historyData,
  });
});

/**
 * Get subscription details by ID
 * @route GET /api/subscriptions/:subscriptionId
 * @access Client
 */
const getSubscriptionById = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getSubscriptionById(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Subscription details retrieved successfully',
    data: subscriptionData,
  });
});

/**
 * Cancel user's active subscription
 * @route POST /api/subscriptions/cancel
 * @access Client
 */
const cancelSubscription = asyncHandler(async (req, res) => {
  const result = await SubscriptionService.cancelSubscription(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: result.message,
    data: result,
  });
});

// Admin Controllers

/**
 * Get all subscriptions (Admin)
 * @route GET /api/admin/subscriptions
 * @access Admin
 */
const getAllSubscriptions = asyncHandler(async (req, res) => {
  const subscriptionsData = await SubscriptionService.getAllSubscriptions(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'All subscriptions retrieved successfully',
    data: subscriptionsData,
  });
});

/**
 * Get subscription statistics (Admin)
 * @route GET /api/admin/subscriptions/stats
 * @access Admin
 */
const getSubscriptionStats = asyncHandler(async (req, res) => {
  const statsData = await SubscriptionService.getSubscriptionStats();

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Subscription statistics retrieved successfully',
    data: statsData,
  });
});

/**
 * Toggle subscription status (Admin)
 * @route PATCH /api/admin/subscriptions/:subscriptionId/toggle
 * @access Admin
 */
const toggleSubscriptionStatus = asyncHandler(async (req, res) => {
  const result = await SubscriptionService.toggleSubscriptionStatus(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: result.message,
    data: result,
  });
});

/**
 * Get subscription by ID (Admin)
 * @route GET /api/admin/subscriptions/:subscriptionId
 * @access Admin
 */
const getSubscriptionByIdAdmin = asyncHandler(async (req, res) => {
  const subscriptionData = await SubscriptionService.getSubscriptionByIdAdmin(req);

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Subscription details retrieved successfully',
    data: subscriptionData,
  });
});

/**
 * Update expired subscriptions (Admin/System)
 * @route POST /api/admin/subscriptions/update-expired
 * @access Admin
 */
const updateExpiredSubscriptions = asyncHandler(async (req, res) => {
  const result = await SubscriptionService.updateExpiredSubscriptions();

  res.status(HttpStatus.STATUS_CODE.OK).json({
    status: HttpStatus.STATUS.SUCCESS,
    message: 'Expired subscriptions updated successfully',
    data: result,
  });
});

export {
  // Client controllers
  getActiveSubscription,
  getSubscriptionHistory,
  getSubscriptionById,
  cancelSubscription,
  
  // Admin controllers
  getAllSubscriptions,
  getSubscriptionStats,
  toggleSubscriptionStatus,
  getSubscriptionByIdAdmin,
  updateExpiredSubscriptions,
};
