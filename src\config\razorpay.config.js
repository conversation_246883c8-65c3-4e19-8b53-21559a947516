import Razorpay from 'razorpay';
import config from './environment.config.js';

/**
 * Initialize Razorpay instance with API credentials securely loaded
 * from environment-specific configuration.
 *
 * Ensures centralized management of secrets and supports
 * environment separation (development, staging, production).
 */
const razorpay = new Razorpay({
  key_id: config.razorpay_key_id,
  key_secret: config.razorpay_key_secret,
});

export default razorpay;
