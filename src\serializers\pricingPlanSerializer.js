const planDurationOptionSerializer = (planDurationOptionDoc) => {
  const {
    _id,
    label,
    valueInDays,
    features,
    price,
    currency,
    paymentType,
    slug,
    isActive,
    customSort,
    createdAt,
    updatedAt,
  } = planDurationOptionDoc;

  return {
    id: _id,
    label: label,
    valueInDays: valueInDays,
    slug: slug,
    features: features.map((feature) => planFeaturesSerializer(feature)),
    price: price,
    currency: currency,
    paymentType: paymentType,
    isActive: isActive,
    customSort: customSort,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

const planFeaturesSerializer = (planFearturesDoc) => {
  const { _id, name, description, slug, isActive, customSort, createdAt, updatedAt } = planFearturesDoc;

  return {
    id: _id,
    name: name,
    description: description,
    slug: slug,
    isActive: isActive,
    customSort: customSort,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

const pricingPlanSerializer = (pricingPlanDoc) => {
  const { _id, name, slug, description, durations, isActive, isPopular, customSort, createdAt, updatedAt } =
    pricingPlanDoc;

  return {
    id: _id,
    name: name,
    slug: slug,
    description: description,
    durations: durations.map((duration) => planDurationOptionSerializer(duration)),
    isActive: isActive,
    isPopular: isPopular,
    customSort: customSort,
    createdAt: createdAt,
    updatedAt: updatedAt,
  };
};

export { planDurationOptionSerializer, planFeaturesSerializer, pricingPlanSerializer };
