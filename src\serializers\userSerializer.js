import { UserRoles } from '../constants/userRoles.js';
import clientSerializer from './clientSerializer.js';
import { serializeDocument } from './serializeResponse.js';

const userSerializer = (userDoc, options = {}) => {
  const { blacklist = [], allow = [] } = options;

  const user = serializeDocument(userDoc, { blacklist, allow });

  const commonFields = {
    id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber,
    dateOfBirth: user.dateOfBirth,
    roles: user.roles,
    avatar: user.avatar,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };

  // Dynamically pick allowed fields (not in commonFields already)
  const dynamicFields = {};
  for (const key of allow) {
    if (!(key in commonFields) && user[key] !== undefined) {
      dynamicFields[key] = user[key];
    }
  }

  // Conditionally include profile
  let profile = null;
  const profileAllowed = !blacklist.includes('profile') || allow.includes('profile');

  if (profileAllowed && user.profile && user.roles?.includes(UserRoles.CLIENT)) {
    profile = clientSerializer(user.profile);
  }

  return {
    ...commonFields,
    ...dynamicFields,
    ...(profileAllowed ? { profile } : {}),
  };
};

export default userSerializer;
