import express from 'express';

// Import admin route modules
import clientsRoutes from './clients.routes.js';
import dashboardRoutes from './dashboard.routes.js';
import paymentsRoutes from './payments.routes.js';
import subscriptionsRoutes from './subscriptions.routes.js';
import couponsRoutes from './coupons.routes.js';
import pricingRoutes from './pricing/index.js';

const router = express.Router();

/**
 * ===== ADMIN API ROUTES =====
 * All routes require admin authentication and authorization
 */

// Dashboard and analytics
router.use('/dashboard', dashboardRoutes);

// Client management
router.use('/clients', clientsRoutes);

// Payment and subscription management
router.use('/payments', paymentsRoutes);
router.use('/subscriptions', subscriptionsRoutes);

// Coupon management
router.use('/coupons', couponsRoutes);

// Pricing management
router.use('/pricing', pricingRoutes);

export default router;
