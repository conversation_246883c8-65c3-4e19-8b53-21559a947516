import couponModel from '../../models/order/coupon.modal.js';
import { ValidationMessages } from '../../constants/messages.js';
import { ValidationError, NotFoundError } from '../../utils/errorHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import couponSerializer from '../../serializers/couponSerializer.js';
import { DiscountType } from '../../constants/enums.js';
import pricingPlanModel from '../../models/pricing/pricingPlan.modal.js';
import userModel from '../../models/user.model.js';

class CouponService {
  static async createCoupon(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const { code, discountType, discountValue, expiresAt, allowedUsers, usageLimitPerUser } = req.body;

    if (!code || !discountType || !discountValue) {
      throw new ValidationError('Code, discount type, and discount value are required fields');
    }

    const existingCoupon = await couponModel.findOne({ code });
    if (existingCoupon) {
      throw new ValidationError('Coupon with this code already exists');
    }

    const newCoupon = new couponModel({
      code,
      discountType,
      discountValue,
      expiresAt,
      allowedUsers,
      usageLimitPerUser,
    });

    await newCoupon.save();

    return couponSerializer(newCoupon);
  }

  static async getCoupons(req, res) {
    const coupons = await couponModel.find({}).sort({ createdAt: -1 }).lean();

    if (!coupons) {
      throw new NotFoundError('Coupons not found');
    }

    const formattedCoupons = coupons.map((coupon) => couponSerializer(coupon));

    return formattedCoupons;
  }

  static async getCouponById(req, res) {
    const { id } = req.params;

    const coupon = await couponModel.findById(id).lean();

    if (!coupon) {
      throw new NotFoundError('Coupon not found');
    }

    return couponSerializer(coupon);
  }

  static async updateCoupon(req, res) {
    const { id } = req.params;
    const allowed = ['discountType', 'discountValue', 'expiresAt', 'allowedUsers', 'usageLimitPerUser'];

    const updates = {};
    for (const field of allowed) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }

    if (Object.keys(updates).length === 0) {
      throw new ValidationError('No valid fields provided for update', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const coupon = await couponModel.findById(id);
    if (!coupon) {
      throw new NotFoundError('Coupon not found');
    }

    if (updates.discountType && !Object.values(DiscountType.ALL).includes(updates.discountType)) {
      throw new ValidationError('Invalid discount type provided for update ');
    }

    Object.assign(coupon, updates);
    await coupon.save();

    return couponSerializer(coupon);
  }

  static async activateCoupon(req, res) {
    const { id } = req.params;

    const coupon = await couponModel.findById(id);

    if (!coupon) {
      throw new NotFoundError('Coupon not found');
    }

    coupon.isActive = true;
    await coupon.save();

    return couponSerializer(coupon);
  }

  static async deactivateCoupon(req, res) {
    const { id } = req.params;

    const coupon = await couponModel.findById(id);

    if (!coupon) {
      throw new NotFoundError('Coupon not found');
    }

    coupon.isActive = false;
    await coupon.save();

    return couponSerializer(coupon);
  }

  static async deleteCoupon(req, res) {
    const { id } = req.params;
    const coupon = await couponModel.findById(id);

    if (!coupon) {
      throw new NotFoundError('Coupon not found');
    }

    await couponModel.findByIdAndDelete(id);

    return null;
  }

  static async validateCoupon(req, res) {
    if (!req.body) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }

    const userId = req.user.id;
    const { code, planId, durationId } = req.body;

    if (!code || !planId || !durationId) {
      throw new ValidationError('Code, plan ID, and duration ID are required fields');
    }

    const [coupon, user, plan] = await Promise.all([
      couponModel.findOne({ code: code.toUpperCase(), isActive: true }),
      userModel.findById(userId),
      pricingPlanModel.findById(planId).populate({
        path: 'durations',
        model: 'PlanDurationOption',
        match: { _id: durationId },
      }),
    ]);

    if (!coupon) throw new NotFoundError('Coupon not found');
    if (!user) throw new NotFoundError('User not found');
    if (!plan) throw new NotFoundError('Plan not found');

    const selectedDuration = plan.durations?.[0];
    if (!selectedDuration) {
      throw new ValidationError('Selected plan duration not found');
    }

    const price = selectedDuration.price;
    if (typeof price !== 'number' || isNaN(price)) {
      throw new ValidationError('Invalid pricing on selected plan duration');
    }

    // Coupon Expiry
    if (coupon.expiresAt && new Date() > coupon.expiresAt) {
      throw new ValidationError('Coupon has expired');
    }

    // Allowed Users
    if (coupon.allowedUsers.length && !coupon.allowedUsers.some((id) => id.equals(userId))) {
      throw new ValidationError('This coupon is not valid for your account');
    }

    // Check usage limit
    const usageEntry = coupon.usedBy.find((entry) => entry.userId?.toString() === userId.toString());
    const userUsageCount = usageEntry ? usageEntry.usageCount : 0;

    if (userUsageCount >= coupon.usageLimitPerUser) {
      throw new ValidationError('Coupon usage limit reached for your account');
    }

    // Calculate Discount
    let discount = 0;
    if (coupon.discountType === DiscountType.PERCENTAGE) {
      discount = (price * coupon.discountValue) / 100;
    } else if (coupon.discountType === DiscountType.FLAT) {
      discount = coupon.discountValue;
    }

    const finalAmount = Math.max(0, price - discount);

    return {
      originalPrice: price,
      discount,
      finalAmount,
      couponCode: coupon.code,
    };
  }
}

export default CouponService;
