import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import { getDashboardOverview } from '../../controllers/dashboard.controller.js';

const router = express.Router();

// Apply authentication and authorization middleware to all admin dashboard routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get comprehensive dashboard overview with analytics
 * @access  Admin only
 */
router.get('/', getDashboardOverview);

/**
 * @route   GET /api/admin/dashboard/stats
 * @desc    Get detailed statistics for admin dashboard
 * @access  Admin only
 */
router.get('/stats', getDashboardOverview);

export default router;
