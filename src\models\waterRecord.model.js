import mongoose from 'mongoose';

const WaterRecordSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    consumedLiters: {
      type: Number,
      required: true,
    },
    date: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

export default mongoose.models.WaterRecord || mongoose.model('WaterRecord', WaterRecordSchema, 'water_records');
