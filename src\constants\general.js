/**
 * Centralized container for application-wide constant values.
 * This class provides immutable constants to avoid magic strings throughout the codebase.
 */
export class Constants {
  /**
   * HTTP header keys used across API requests.
   * Keeping header keys standardized helps avoid typos and inconsistencies.
   */
  static HEADERS = Object.freeze({
    /** Authorization header used for passing access tokens or credentials. */
    AUTHORIZATION: 'Authorization',
  });
}

// Freeze the class itself to prevent any further modifications.
Object.freeze(Constants);
