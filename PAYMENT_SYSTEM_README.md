# Payment System with Razorpay Integration

This document describes the complete payment and subscription management system integrated with Razorpay.

## Features

- **One-time Payment Support**: Currently supports one-time payments for plan subscriptions
- **Razorpay Integration**: Secure payment processing through Razorpay
- **Subscription Management**: Automatic subscription activation and management
- **Coupon System**: Support for discount coupons
- **Admin Dashboard**: Complete admin interface for payment and subscription management
- **Webhook Support**: Real-time payment status updates via Razorpay webhooks

## Environment Variables

Add the following environment variables to your `.env` file:

```env
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret (optional, defaults to key_secret)
```

## API Endpoints

### Client APIs

#### Payment APIs
- `GET /api/payments/plans` - Get available pricing plans
- `POST /api/payments/create-order` - Create payment order
- `POST /api/payments/verify` - Verify payment and activate subscription
- `POST /api/payments/validate-coupon` - Validate coupon code
- `GET /api/payments/history` - Get payment history
- `GET /api/payments/:orderId` - Get payment details

#### Subscription APIs
- `GET /api/subscriptions/active` - Get active subscription
- `GET /api/subscriptions/history` - Get subscription history
- `GET /api/subscriptions/:subscriptionId` - Get subscription details
- `POST /api/subscriptions/cancel` - Cancel subscription

### Admin APIs

#### Payment Management
- `GET /api/admin/payments` - Get all payments with filters
- `GET /api/admin/payments/stats` - Get payment statistics
- `GET /api/admin/payments/:orderId` - Get payment details

#### Subscription Management
- `GET /api/admin/subscriptions` - Get all subscriptions with filters
- `GET /api/admin/subscriptions/stats` - Get subscription statistics
- `GET /api/admin/subscriptions/:subscriptionId` - Get subscription details
- `PATCH /api/admin/subscriptions/:subscriptionId/toggle` - Toggle subscription status
- `POST /api/admin/subscriptions/update-expired` - Update expired subscriptions

### Webhook
- `POST /api/webhooks/razorpay` - Razorpay webhook handler

## Usage Flow

### 1. Create Payment Order

```javascript
POST /api/payments/create-order
{
  "planId": "plan_id_here",
  "durationId": "duration_id_here",
  "couponCode": "OPTIONAL_COUPON" // optional
}
```

Response:
```javascript
{
  "status": "success",
  "data": {
    "orderId": "internal_order_id",
    "razorpayOrderId": "order_razorpay_id",
    "amount": 1000, // in paise
    "currency": "INR",
    "key": "razorpay_key_id"
  }
}
```

### 2. Frontend Payment Integration

Use the response data to initialize Razorpay checkout:

```javascript
const options = {
  key: response.data.key,
  amount: response.data.amount,
  currency: response.data.currency,
  order_id: response.data.razorpayOrderId,
  handler: function(response) {
    // Verify payment on backend
    verifyPayment(response);
  }
};

const rzp = new Razorpay(options);
rzp.open();
```

### 3. Verify Payment

```javascript
POST /api/payments/verify
{
  "razorpay_order_id": "order_id_from_razorpay",
  "razorpay_payment_id": "payment_id_from_razorpay",
  "razorpay_signature": "signature_from_razorpay"
}
```

Response:
```javascript
{
  "status": "success",
  "data": {
    "success": true,
    "message": "Payment completed successfully",
    "order": { /* order details */ },
    "subscription": { /* subscription details */ }
  }
}
```

## Database Models

### PlanPurchase (Order)
- Stores payment order information
- Links to Razorpay order and payment IDs
- Tracks payment status and metadata

### Subscription
- Manages user subscriptions
- Links to plan, duration, and order
- Tracks subscription status and validity

### PricingPlan & PlanDurationOption
- Existing models for plan management
- Extended to support payment integration

## Security Features

- **Signature Verification**: All payments are verified using Razorpay signatures
- **Webhook Verification**: Webhook events are verified for authenticity
- **Role-based Access**: Separate client and admin endpoints
- **Input Validation**: All inputs are validated and sanitized

## Error Handling

The system includes comprehensive error handling for:
- Invalid payment signatures
- Expired or invalid coupons
- Duplicate subscriptions
- Payment failures
- Network issues

## Monitoring & Logging

- All payment events are logged
- Webhook events are tracked
- Failed payments are recorded for analysis
- Admin dashboard provides comprehensive statistics

## Testing

To test the payment system:

1. Set up Razorpay test credentials
2. Create test plans and durations
3. Use Razorpay test card numbers for payments
4. Verify webhook functionality with ngrok or similar tools

## Production Deployment

Before going live:

1. Replace test Razorpay credentials with live ones
2. Set up proper webhook URLs in Razorpay dashboard
3. Configure SSL certificates for webhook endpoints
4. Set up monitoring and alerting for payment failures
5. Test the complete flow with small amounts

## Support

For issues or questions:
- Check the API documentation at `/` endpoint
- Review logs for error details
- Verify Razorpay dashboard for payment status
- Contact development team for technical support
