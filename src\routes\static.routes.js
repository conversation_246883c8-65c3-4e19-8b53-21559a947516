import express from 'express';
import path from 'path';

const router = express.Router();

/**
 * @route   GET /
 * @desc    Serve the main welcome/landing page
 * @access  Public
 */
router.get('/', (req, res) => {
  res.sendFile('index.html', { root: 'public' });
});

/**
 * @route   GET /health
 * @desc    Serve the health check dashboard page
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.sendFile('health.html', { root: 'public' });
});

/**
 * @route   GET /test-health
 * @desc    Serve the health check testing interface
 * @access  Public
 */
router.get('/test-health', (req, res) => {
  res.sendFile('test-health.html', { root: 'public' });
});

/**
 * @route   GET /docs
 * @desc    Serve API documentation page (if exists)
 * @access  Public
 */
router.get('/docs', (req, res) => {
  // Check if docs.html exists, otherwise redirect to main page
  res.sendFile('docs.html', { root: 'public' }, (err) => {
    if (err) {
      res.redirect('/');
    }
  });
});

/**
 * @route   GET /status
 * @desc    Redirect to health dashboard
 * @access  Public
 */
router.get('/status', (req, res) => {
  res.redirect('/health');
});

/**
 * @route   GET /monitor
 * @desc    Redirect to health dashboard
 * @access  Public
 */
router.get('/monitor', (req, res) => {
  res.redirect('/health');
});

/**
 * @route   GET /dashboard
 * @desc    Redirect to main page (client dashboard would be separate)
 * @access  Public
 */
router.get('/dashboard', (req, res) => {
  res.redirect('/');
});

export default router;
