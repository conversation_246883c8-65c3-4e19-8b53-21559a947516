import express from 'express';
import {
  createCoupon,
  getCoupons,
  getCouponById,
  updateCoupon,
  deleteCoupon,
  activateCoupon,
  deactivateCoupon,
} from '../../controllers/order/coupon.controller.js';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { UserRoles } from '../../constants/userRoles.js';

const router = express.Router();

// Apply authentication and authorization middleware to all admin coupon routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/admin/coupons
 * @desc    Create a new coupon
 * @access  Admin only
 */
router.post('/', createCoupon);

/**
 * @route   GET /api/admin/coupons
 * @desc    Get all coupons with pagination and filters
 * @access  Admin only
 */
router.get('/', getCoupons);

/**
 * @route   GET /api/admin/coupons/:id
 * @desc    Get a specific coupon by ID
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getCouponById);

/**
 * @route   PUT /api/admin/coupons/:id
 * @desc    Update a specific coupon by ID
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updateCoupon);

/**
 * @route   PATCH /api/admin/coupons/:id/activate
 * @desc    Activate a coupon
 * @access  Admin only
 */
router.patch('/:id/activate', validateMongoId(), activateCoupon);

/**
 * @route   PATCH /api/admin/coupons/:id/deactivate
 * @desc    Deactivate a coupon
 * @access  Admin only
 */
router.patch('/:id/deactivate', validateMongoId(), deactivateCoupon);

/**
 * @route   DELETE /api/admin/coupons/:id
 * @desc    Delete a specific coupon by ID
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deleteCoupon);

export default router;
