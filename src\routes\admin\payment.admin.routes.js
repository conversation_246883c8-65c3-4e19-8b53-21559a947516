import express from 'express';
import {
  getAllSubscriptions,
  getSubscriptionStats,
  toggleSubscriptionStatus,
  getSubscriptionByIdAdmin,
  updateExpiredSubscriptions,
} from '../../controllers/order/subscription.controller.js';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';
import { UserRoles } from '../../constants/userRoles.js';
import asyncHandler from '../../utils/asyncHandler.js';
import { HttpStatus } from '../../constants/httpStatus.js';
import planPurchaseModel from '../../models/order/order.modal.js';
import { paymentHistorySerializer } from '../../serializers/paymentSerializer.js';

const router = express.Router();

// Apply global middlewares: JWT + Role-Based Authorization for Admins
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

// Payment Management Routes

/**
 * @route   GET /api/admin/payments
 * @desc    Get all payment orders with filters
 * @access  Admin only
 * @query   { page?, limit?, status?, userId?, planId?, startDate?, endDate? }
 */
router.get(
  '/payments',
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, status, userId, planId, startDate, endDate } = req.query;

    const skip = (page - 1) * limit;
    const filter = {};

    // Apply filters
    if (status) filter.status = status;
    if (userId) filter.userId = userId;
    if (planId) filter.planId = planId;
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = new Date(startDate);
      if (endDate) filter.createdAt.$lte = new Date(endDate);
    }

    const orders = await planPurchaseModel
      .find(filter)
      .populate([
        {
          path: 'userId',
          select: 'email firstName lastName',
        },
        {
          path: 'planId',
          select: 'name slug',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price',
        },
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    const total = await planPurchaseModel.countDocuments(filter);

    res.status(HttpStatus.STATUS_CODE.OK).json({
      status: HttpStatus.STATUS.SUCCESS,
      message: 'Payment orders retrieved successfully',
      data: {
        orders: orders.map((order) => ({
          id: order._id,
          user: order.userId
            ? {
                id: order.userId._id,
                email: order.userId.email,
                name: `${order.userId.firstName || ''} ${order.userId.lastName || ''}`.trim(),
              }
            : null,
          plan: order.planId
            ? {
                id: order.planId._id,
                name: order.planId.name,
                slug: order.planId.slug,
              }
            : null,
          duration: order.durationId
            ? {
                id: order.durationId._id,
                label: order.durationId.label,
                valueInDays: order.durationId.valueInDays,
                price: order.durationId.price,
              }
            : null,
          razorpayOrderId: order.razorpay_order_id,
          razorpayPaymentId: order.razorpay_payment_id,
          amount: order.amount,
          currency: order.currency,
          status: order.status,
          paymentDate: order.paymentDate,
          createdAt: order.createdAt,
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  })
);

/**
 * @route   GET /api/admin/payments/stats
 * @desc    Get payment statistics
 * @access  Admin only
 */
router.get(
  '/payments/stats',
  asyncHandler(async (req, res) => {
    const stats = await planPurchaseModel.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
        },
      },
    ]);

    const totalPayments = await planPurchaseModel.countDocuments();
    const totalRevenue = await planPurchaseModel.aggregate([
      {
        $match: { status: 'success' },
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' },
        },
      },
    ]);

    res.status(HttpStatus.STATUS_CODE.OK).json({
      status: HttpStatus.STATUS.SUCCESS,
      message: 'Payment statistics retrieved successfully',
      data: {
        total: totalPayments,
        totalRevenue: totalRevenue[0]?.total || 0,
        statusBreakdown: stats.reduce((acc, stat) => {
          acc[stat._id] = {
            count: stat.count,
            totalAmount: stat.totalAmount,
          };
          return acc;
        }, {}),
      },
    });
  })
);

/**
 * @route   GET /api/admin/payments/:orderId
 * @desc    Get payment order details by ID
 * @access  Admin only
 */
router.get(
  '/payments/:orderId',
  validateMongoId(),
  asyncHandler(async (req, res) => {
    const { orderId } = req.params;

    const order = await planPurchaseModel
      .findById(orderId)
      .populate([
        {
          path: 'userId',
          select: 'email firstName lastName phoneNumber',
        },
        {
          path: 'planId',
          select: 'name slug description',
        },
        {
          path: 'durationId',
          select: 'label valueInDays price currency paymentType',
        },
      ])
      .lean();

    if (!order) {
      return res.status(HttpStatus.STATUS_CODE.NOT_FOUND).json({
        status: HttpStatus.STATUS.ERROR,
        message: 'Payment order not found',
      });
    }

    res.status(HttpStatus.STATUS_CODE.OK).json({
      status: HttpStatus.STATUS.SUCCESS,
      message: 'Payment order details retrieved successfully',
      data: {
        id: order._id,
        user: order.userId
          ? {
              id: order.userId._id,
              email: order.userId.email,
              firstName: order.userId.firstName,
              lastName: order.userId.lastName,
              phoneNumber: order.userId.phoneNumber,
            }
          : null,
        plan: order.planId
          ? {
              id: order.planId._id,
              name: order.planId.name,
              slug: order.planId.slug,
              description: order.planId.description,
            }
          : null,
        duration: order.durationId
          ? {
              id: order.durationId._id,
              label: order.durationId.label,
              valueInDays: order.durationId.valueInDays,
              price: order.durationId.price,
              currency: order.durationId.currency,
              paymentType: order.durationId.paymentType,
            }
          : null,
        razorpayOrderId: order.razorpay_order_id,
        razorpayPaymentId: order.razorpay_payment_id,
        razorpaySignature: order.razorpay_signature,
        amount: order.amount,
        currency: order.currency,
        paymentType: order.paymentType,
        status: order.status,
        paymentDate: order.paymentDate,
        rawResponse: order.rawResponse,
        metadata: order.metadata,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
      },
    });
  })
);

// Subscription Management Routes

/**
 * @route   GET /api/admin/subscriptions
 * @desc    Get all subscriptions with filters
 * @access  Admin only
 * @query   { page?, limit?, status?, planId?, userId?, isActive?, startDate?, endDate? }
 */
router.get('/subscriptions', getAllSubscriptions);

/**
 * @route   GET /api/admin/subscriptions/stats
 * @desc    Get subscription statistics
 * @access  Admin only
 */
router.get('/subscriptions/stats', getSubscriptionStats);

/**
 * @route   POST /api/admin/subscriptions/update-expired
 * @desc    Update expired subscriptions
 * @access  Admin only
 */
router.post('/subscriptions/update-expired', updateExpiredSubscriptions);

/**
 * @route   GET /api/admin/subscriptions/:subscriptionId
 * @desc    Get subscription details by ID
 * @access  Admin only
 */
router.get('/subscriptions/:subscriptionId', validateMongoId(), getSubscriptionByIdAdmin);

/**
 * @route   PATCH /api/admin/subscriptions/:subscriptionId/toggle
 * @desc    Toggle subscription status (activate/deactivate)
 * @access  Admin only
 * @body    { action: 'activate' | 'deactivate' }
 */
router.patch('/subscriptions/:subscriptionId/toggle', validateMongoId(), toggleSubscriptionStatus);

export default router;
