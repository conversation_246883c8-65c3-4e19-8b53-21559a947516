import mongoose from 'mongoose';
import { PricingUnit } from '../../constants/enums.js';

const planDurationOptionSchema = new mongoose.Schema(
  {
    label: {
      type: String,
      required: true,
    },
    valueInDays: {
      type: Number,
      required: true,
    },
    features: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Feature',
      },
    ],
    paymentType: {
      type: String,
      enum: PricingUnit.ALL,
      default: PricingUnit.ONE_TIME,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      default: 'INR',
    },
    slug: {
      type: String,
      required: true,
      unique: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    customSort: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

export default mongoose.model('PlanDurationOption', planDurationOptionSchema, 'plan_duration_options');
