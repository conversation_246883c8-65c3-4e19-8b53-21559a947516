import jwt from 'jsonwebtoken';
import config from '../config/environment.config.js';
import userRefreshTokenModal from '../models/userRefreshToken.modal.js';

/**
 * Generates JWT access and refresh tokens for a given user.
 * Access token expires in 1 day; refresh token expires in 30 days.
 * Stores the refresh token in DB, replacing any existing token for the user.
 *
 * @param {Object} user - User object containing _id, roles, email, isVerified.
 * @returns {Object} - Contains accessToken, accessTokenExp, refreshToken, refreshTokenExp.
 */
const generateTokens = async (user) => {
  // Define the JWT payload with necessary user claims
  const payload = {
    _id: user._id,
    roles: user.roles,
    email: user.email,
    isVerified: user.isVerified,
  };

  // Access Token expiration time (1 day in seconds)
  const accessTokenExp = Math.floor(Date.now() / 1000) + 1 * 24 * 60 * 60;

  // Generate Access Token using configured secret and algorithm
  const accessToken = jwt.sign({ ...payload, exp: accessTokenExp }, config.jwt_access_token_secret_key, {
    algorithm: config.jwt_algorithm,
  });

  // Refresh Token expiration time (30 days in seconds)
  const refreshTokenExp = Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60;

  // Generate Refresh Token using configured secret and algorithm
  const refreshToken = jwt.sign({ ...payload, exp: refreshTokenExp }, config.jwt_refresh_token_secret_key, {
    algorithm: config.jwt_algorithm,
  });

  // Remove any existing refresh token for the user to ensure single active token
  await userRefreshTokenModal.findOneAndDelete({ userId: user._id });

  // Persist the new refresh token in the database
  await new userRefreshTokenModal({ userId: user._id, token: refreshToken }).save();

  // Return tokens and their expiration times for client use
  return {
    accessToken,
    accessTokenExp,
    refreshToken,
    refreshTokenExp,
  };
};

export default generateTokens;
