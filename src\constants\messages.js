/**
 * ValidationMessages class centralizes user-facing messages related to
 * API validations, authentication, tokens, and server statuses.
 * This promotes consistency in response messages and eases maintainability.
 */
export class ValidationMessages {
  /**
   * General purpose messages for CRUD operations, errors, and common HTTP response cases.
   */
  static GENERAL = {
    SUCCESS: 'The request was completed successfully.',
    CREATED: 'The resource has been created successfully.',
    UPDATED: 'The resource has been updated successfully.',
    FETCHED: 'The resource was retrieved successfully.',
    DELETED: 'The resource has been deleted successfully.',
    NO_CONTENT: 'The request was successful, but there is no content to return.',
    FAILED: 'The request could not be completed.',
    BAD_REQUEST: 'The request was invalid or malformed.',
    INVALID_REQUEST: 'The request is invalid. Please review the submitted data.',
    INVALID_REQUEST_BODY: 'The request body is invalid. Please check the data format.',
    MISSING_FIELDS: 'Required fields are missing from the request.',
    NOT_FOUND: 'The requested resource could not be found.',
    ROUTE: (url) => `The route "${url}" does not exist.`,
    CONFLICT: 'A conflict occurred with the current state of the resource.',
    UNPROCESSABLE_ENTITY: 'The request is well-formed but contains semantic errors.',
    UNAUTHORIZED: 'You are not authorized to access this resource. Please log in.',
    FORBIDDEN: 'Access is forbidden. You do not have permission to perform this action.',
    SERVER_ERROR: 'An unexpected error occurred on the server. Please try again later.',
    TOO_MANY_REQUEST: 'Too many requests. Please wait and try again later.',
  };

  /**
   * Authentication-specific messages related to login, OTP, password management,
   * email verification, session management, and permissions.
   */
  static AUTHENTICATION = {
    ACCOUNT_LOGIN_SUCCESS: 'You have logged in successfully.',
    PASSWORD_CHANGED_SUCCESS: 'Your password has been changed successfully.',
    OTP_SENT: 'A one-time password (OTP) has been sent successfully.',
    OTP_VERIFIED: 'The OTP has been verified successfully.',
    PASSWORD_RESET_SUCCESS: 'Your password has been reset successfully.',
    LOGOUT_SUCCESS: 'You have re logged out successfully.',
    VERIFICATION_EMAIL_SENT: 'A verification email has been sent successfully.',
    EMAIL_VERIFIED: 'Your email has been verified successfully.',
    INVALID_CREDENTIALS: 'The email or password provided is incorrect.',
    OLD_PASSWORD_NOT_MATCHED: 'The old password you entered is incorrect.',
    ACCOUNT_NOT_FOUND: 'No account was found with the provided credentials.',
    ACCOUNT_NOT_VERIFIED: 'Your account has not been verified yet.',
    DEACTIVATED: 'Your account has been deactivated. Please contact support.',
    SESSION_EXPIRED: 'Your session has expired. Please log in again.',
    INSUFFICIENT_PERMISSIONS: 'You do not have sufficient permissions to access this resource.',
    PASSWORD_TOO_SHORT: 'Your password must be at least 6 characters long.',
    PASSWORD_MISMATCH: 'The passwords do not match.',
    OTP_EXPIRED: 'The OTP has expired. Please request a new one.',
    INVALID_OTP: 'The OTP entered is invalid.',
    TOO_MANY_OTP_ATTEMPTS: 'Too many failed attempts. Please request a new OTP.',
    EMAIL_REQUIRED: 'Email is required to proceed.',
    OTP_REQUIRED: 'OTP is required to continue.',
    PASSWORD_REQUIRED: 'Password is required to continue.',
    TOKEN_AND_PASSWORD_REQUIRED: 'Both token and password are required.',
    INVALID_OR_EXPIRED_TOKEN: 'The verification token is either invalid or has expired.',
    EMAIL_ALREADY_VERIFIED: 'This email has already been verified.',
    INVALID_EMAIL_FORMAT: 'The email address provided is not valid.',
  };

  /**
   * Messages related to refresh tokens and JWT authentication strategies.
   */
  static TOKEN = {
    VALID_REFRESH_TOKEN: 'The refresh token is valid.',
    ERROR_JWT_STRATEGY: 'Error in JWT strategy.',
    REFRESH_TOKEN_REQUIRED: 'Refresh token is required to continue',
    JWT_SECRET_MISSING: 'The JWT refresh token secret is missing in the environment variables.',
    INVALID_REFRESH_TOKEN: 'The provided refresh token is invalid.',
    REFRESH_TOKEN_VERIFICATION_ERROR: (message) => `There was an error verifying the refresh token: ${message}`,
  };

  /**
   * Server lifecycle and status messages, including startup, health checks, database connections,
   * and error handling.
   */
  static SERVER = {
    STARTED: (port) => `The server is running on port ${port}.`,
    HEALTHY: 'The server is running and healthy.',
    DATABASE_CONNECTED: 'Successfully connected to the database.',
    ERROR: 'An unexpected error occurred on the server.',
    UNHANDLED: 'Unhandled rejection detected. Shutting down gracefully...',
    DATABASE_ERROR: 'Failed to connect to the database.',
  };
}
