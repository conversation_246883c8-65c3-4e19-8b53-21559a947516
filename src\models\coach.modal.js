import mongoose from 'mongoose';

const CoachSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Reference to the User model
    required: true,
  },
  certifications: [
    {
      type: String, // Example: 'Certified Personal Trainer', 'Yoga Instructor', etc.
    },
  ],
  expertise: {
    type: [String], // Types of training expertise (e.g., Weight Training, Yoga, CrossFit)
  },
  availableHours: [
    {
      day: { type: String, required: true },
      startTime: { type: String },
      endTime: { type: String },
    },
  ],
  // List of clients assigned to the coach
  clients: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Client',
    },
  ],
});

// Index for faster queries
CoachSchema.index({ userId: 1 });

export default mongoose.model('Coach', CoachSchema, 'coaches');
