import transporter from '../config/email.config.js';
import emailVerificationModel from '../models/emailVerification.modal.js';
import config from '../config/environment.config.js';
import generateOtp from './generateOtp.js';
import emailVerificationOtpTemplate from './template/emailVerificationOtpTemplate.js';

const sendEmailVerificationOtp = async (req, user) => {
  const otp = generateOtp();

  await new emailVerificationModel({ userId: user._id, otp }).save();

  const otpVerificationLink = `${config.frontend_host}/account/verify-email`;

  const emailHtml = emailVerificationOtpTemplate(user, otp, otpVerificationLink);

  await transporter.sendMail({
    from: config.email_from,
    to: user.email,
    subject: 'OTP - Verify your account',
    html: emailHtml,
  });

  return otp;
};

export default sendEmailVerificationOtp;
