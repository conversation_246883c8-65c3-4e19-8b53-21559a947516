// Health Monitoring Dashboard - Streamlined JavaScript
document.addEventListener('DOMContentLoaded', function () {
  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach((link) => {
    link.addEventListener('click', function (e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        targetSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    });
  });

  // Add scroll effect to navbar
  window.addEventListener('scroll', function () {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
      navbar.style.background = 'rgba(255, 255, 255, 0.98)';
      navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
      navbar.style.background = 'rgba(255, 255, 255, 0.95)';
      navbar.style.boxShadow = 'none';
    }
  });

  // Initialize health monitoring
  initializeHealthMonitoring();

  // Auto-refresh health checks every 30 seconds
  setInterval(performHealthChecks, 30000);

  // Initial health check after page load
  setTimeout(performHealthChecks, 1000);
});

// Initialize health monitoring system
function initializeHealthMonitoring() {
  // Initialize real-time metrics display
  updateMetricDisplay('system-status', 'Initializing...', 'checking');
  updateMetricDisplay('api-status', 'Checking...', 'checking');
  updateMetricDisplay('database-status', 'Connecting...', 'checking');
  updateMetricDisplay('performance-status', 'Analyzing...', 'checking');
}

// Perform comprehensive health checks
async function performHealthChecks() {
  const healthEndpoints = [
    { url: '/api-health', type: 'basic' },
    { url: '/api/health', type: 'comprehensive' },
    { url: '/api/health/detailed', type: 'detailed' },
  ];

  const results = await Promise.allSettled(
    healthEndpoints.map((endpoint) => checkHealthEndpoint(endpoint.url, endpoint.type))
  );

  // Process results and update dashboard
  processHealthResults(results);
}

// Check individual health endpoint
async function checkHealthEndpoint(endpoint, type) {
  const startTime = performance.now();

  try {
    const response = await fetch(endpoint);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.status !== 'Success' && data.status !== 'success') {
      throw new Error('Health check returned non-success status');
    }

    return {
      endpoint,
      type,
      status: 'healthy',
      responseTime,
      data: data.data || {},
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    return {
      endpoint,
      type,
      status: 'unhealthy',
      responseTime,
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
}

// Process health check results and update dashboard
function processHealthResults(results) {
  let overallHealth = 'healthy';
  let totalResponseTime = 0;
  let healthyCount = 0;
  let latestData = null;

  results.forEach((result) => {
    if (result.status === 'fulfilled') {
      const healthResult = result.value;
      totalResponseTime += healthResult.responseTime;

      if (healthResult.status === 'healthy') {
        healthyCount++;
        if (healthResult.data && Object.keys(healthResult.data).length > 0) {
          latestData = healthResult.data;
        }
      } else {
        overallHealth = 'unhealthy';
      }

      // Update individual endpoint status
      updateEndpointStatus(healthResult);
    } else {
      overallHealth = 'unhealthy';
    }
  });

  // Update overall system metrics
  updateSystemMetrics(overallHealth, totalResponseTime / results.length, healthyCount, latestData);
}

// Update individual endpoint status
function updateEndpointStatus(healthResult) {
  const { endpoint, status, responseTime, error } = healthResult;

  // Map endpoints to their display elements
  const endpointMap = {
    '/api-health': 'basic',
    '/api/health': 'comprehensive',
    '/api/health/detailed': 'detailed',
  };

  const endpointType = endpointMap[endpoint];
  if (!endpointType) return;

  const statusElement = document.getElementById(`${endpointType}-health-status`);
  const resultElement = document.getElementById(`${endpointType}-health-result`);

  if (statusElement) {
    if (status === 'healthy') {
      statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Online</span>';
      statusElement.className = 'health-status online';
    } else {
      statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Offline</span>';
      statusElement.className = 'health-status offline';
    }
  }

  if (resultElement) {
    if (status === 'healthy') {
      resultElement.innerHTML = `
        <div class="health-success">
          <div class="response-time">
            <i class="fas fa-clock"></i>
            <span>${responseTime}ms</span>
          </div>
          <div class="metric-item">Status: Operational</div>
        </div>
      `;
    } else {
      resultElement.innerHTML = `
        <div class="error-result">
          <i class="fas fa-exclamation-triangle"></i>
          <span>Error: ${error}</span>
        </div>
      `;
    }
  }
}

// Update system metrics dashboard
function updateSystemMetrics(overallHealth, avgResponseTime, healthyCount, latestData) {
  // Update overall system status
  updateMetricDisplay(
    'system-status',
    overallHealth === 'healthy' ? 'Operational' : 'Issues Detected',
    overallHealth === 'healthy' ? 'online' : 'offline'
  );

  // Update API status
  updateMetricDisplay(
    'api-status',
    `${healthyCount}/3 Healthy`,
    healthyCount === 3 ? 'online' : healthyCount > 0 ? 'checking' : 'offline'
  );

  // Update average response time
  updateMetricDisplay('response-time', `${Math.round(avgResponseTime)}ms`, 'online');

  // Update detailed metrics from latest data
  if (latestData) {
    updateDetailedMetrics(latestData);
  }

  // Update hero section stats
  updateHeroStats(overallHealth, avgResponseTime, healthyCount, latestData);
}

// Update metric display helper
function updateMetricDisplay(elementId, value, status) {
  const element = document.getElementById(elementId);
  if (element) {
    element.textContent = value;
    element.className = `metric-value status-${status}`;
  }
}

// Update detailed metrics from health data
function updateDetailedMetrics(data) {
  // Update uptime
  if (data.uptime) {
    const uptime = typeof data.uptime === 'object' ? data.uptime.formatted : formatUptime(data.uptime);
    updateMetricDisplay('uptime-value', uptime, 'online');
  }

  // Update memory usage
  if (data.memory) {
    const memory = data.memory;
    const used = memory.heapUsed || memory.used || 0;
    const total = memory.heapTotal || memory.total || 0;
    const percentage = total > 0 ? Math.round((used / total) * 100) : 0;
    updateMetricDisplay(
      'memory-value',
      `${used}MB (${percentage}%)`,
      percentage > 80 ? 'offline' : percentage > 60 ? 'checking' : 'online'
    );
    updateMetricDisplay(
      'memory-usage',
      `${used}MB (${percentage}%)`,
      percentage > 80 ? 'offline' : percentage > 60 ? 'checking' : 'online'
    );
  }

  // Update database status
  if (data.services && data.services.database) {
    updateMetricDisplay(
      'database-status',
      data.services.database,
      data.services.database === 'connected' ? 'online' : 'offline'
    );
  }

  // Update additional live metrics
  updateLiveMetrics(data);
}

// Update live metrics dashboard
function updateLiveMetrics(data) {
  // Calculate health score
  let healthScore = 100;

  if (data.memory) {
    const memory = data.memory;
    const used = memory.heapUsed || memory.used || 0;
    const total = memory.heapTotal || memory.total || 0;
    const percentage = total > 0 ? Math.round((used / total) * 100) : 0;

    if (percentage > 80) healthScore -= 30;
    else if (percentage > 60) healthScore -= 15;
  }

  if (data.services) {
    if (data.services.database !== 'connected') healthScore -= 25;
    if (data.services.server !== 'running') healthScore -= 25;
  }

  updateMetricDisplay(
    'health-score',
    `${Math.max(0, healthScore)}%`,
    healthScore >= 90 ? 'online' : healthScore >= 70 ? 'checking' : 'offline'
  );
}

// Update hero section statistics
function updateHeroStats(overallHealth, avgResponseTime, healthyCount, latestData) {
  // Update hero response time
  const heroResponseTime = document.getElementById('hero-response-time');
  if (heroResponseTime) {
    heroResponseTime.textContent = `${Math.round(avgResponseTime)}ms`;
  }

  // Update hero status
  const heroStatus = document.getElementById('hero-status');
  if (heroStatus) {
    heroStatus.textContent = overallHealth === 'healthy' ? 'Operational' : 'Issues';
    heroStatus.className = overallHealth === 'healthy' ? 'stat-number status-online' : 'stat-number status-offline';
  }

  // Update hero visual indicators
  updateHeroVisualIndicators(overallHealth, healthyCount, latestData);
}

// Update hero visual indicators
function updateHeroVisualIndicators(overallHealth, healthyCount, latestData) {
  // Update system indicator
  const systemIndicator = document.getElementById('hero-system-indicator');
  if (systemIndicator) {
    const statusDot = systemIndicator.querySelector('.status-dot');
    const statusText = systemIndicator.querySelector('span');

    if (statusDot && statusText) {
      statusDot.className = `status-dot ${overallHealth === 'healthy' ? 'online' : 'offline'}`;
      statusText.textContent = overallHealth === 'healthy' ? 'System Operational' : 'System Issues';
    }
  }

  // Update API status
  const apiStatus = document.getElementById('hero-api-status');
  if (apiStatus) {
    apiStatus.textContent = `${healthyCount}/3 APIs Online`;
  }

  // Update database status
  const dbStatus = document.getElementById('hero-db-status');
  if (dbStatus && latestData && latestData.services) {
    const dbConnected = latestData.services.database === 'connected';
    dbStatus.textContent = dbConnected ? 'Database Connected' : 'Database Disconnected';
  }

  // Update memory status
  const memoryStatus = document.getElementById('hero-memory-status');
  if (memoryStatus && latestData && latestData.memory) {
    const memory = latestData.memory;
    const used = memory.heapUsed || memory.used || 0;
    const total = memory.heapTotal || memory.total || 0;
    const percentage = total > 0 ? Math.round((used / total) * 100) : 0;

    let status = 'Normal';
    if (percentage > 80) status = 'High';
    else if (percentage > 60) status = 'Moderate';

    memoryStatus.textContent = `Memory: ${status} (${percentage}%)`;
  }
}

// Format uptime in seconds to human readable format
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

// Add CSS for health monitoring
const healthStyle = document.createElement('style');
healthStyle.textContent = `
    .health-status.checking {
        color: #f59e0b;
    }

    .status-online {
        color: #10b981 !important;
    }

    .status-offline {
        color: #ef4444 !important;
    }

    .status-checking {
        color: #f59e0b !important;
    }
`;
document.head.appendChild(healthStyle);
