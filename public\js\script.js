// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function () {
  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll('.nav-link');
  navLinks.forEach((link) => {
    link.addEventListener('click', function (e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      const targetSection = document.querySelector(targetId);
      if (targetSection) {
        targetSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    });
  });

  // Add scroll effect to navbar
  window.addEventListener('scroll', function () {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
      navbar.style.background = 'rgba(255, 255, 255, 0.98)';
      navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
      navbar.style.background = 'rgba(255, 255, 255, 0.95)';
      navbar.style.boxShadow = 'none';
    }
  });

  // Animate API cards on scroll
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
  };

  const observer = new IntersectionObserver(function (entries) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe API categories
  const apiCategories = document.querySelectorAll('.api-category');
  apiCategories.forEach((category) => {
    category.style.opacity = '0';
    category.style.transform = 'translateY(30px)';
    category.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(category);
  });

  // Observe overview cards
  const overviewCards = document.querySelectorAll('.overview-card');
  overviewCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
    observer.observe(card);
  });

  // Add click handlers for API endpoints
  const apiEndpoints = document.querySelectorAll('.api-endpoint');
  apiEndpoints.forEach((endpoint) => {
    endpoint.addEventListener('click', function () {
      const method = this.querySelector('.endpoint-method').textContent;
      const path = this.querySelector('.endpoint-path').textContent;
      const desc = this.querySelector('.endpoint-desc').textContent;

      // Create a simple modal or alert with endpoint details
      showEndpointDetails(method, path, desc);
    });
  });

  // Update stats with animation
  animateStats();

  // Auto-refresh health checks every 30 seconds
  setInterval(refreshAllHealthChecks, 30000);

  // Initial health check after page load
  setTimeout(refreshAllHealthChecks, 1000);
});

// Health check function (legacy - keeping for compatibility)
async function checkHealth(endpoint) {
  const statusElement =
    endpoint === '/api-health' ? document.getElementById('api-status') : document.getElementById('db-status');

  try {
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Checking...</span>';
    statusElement.className = 'health-status checking';

    const response = await fetch(endpoint);
    const data = await response.json();

    if (response.ok && data.status === 'success') {
      statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Online</span>';
      statusElement.className = 'health-status online';
    } else {
      throw new Error('Health check failed');
    }
  } catch (error) {
    statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Offline</span>';
    statusElement.className = 'health-status offline';
  }
}

// Enhanced health endpoint testing function
async function testHealthEndpoint(endpoint, statusElementId, resultElementId) {
  const statusElement = document.getElementById(statusElementId);
  const resultElement = document.getElementById(resultElementId);

  try {
    // Update status to checking
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Checking...</span>';
    statusElement.className = 'health-status checking';

    // Clear previous results
    resultElement.innerHTML = '';

    const startTime = performance.now();
    const response = await fetch(endpoint);
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);

    const data = await response.json();

    if (response.ok && (data.status === 'Success' || data.status === 'success')) {
      // Update status to online
      statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Online</span>';
      statusElement.className = 'health-status online';

      // Display results
      displayHealthResult(resultElement, data, responseTime, true);

      // Update dashboard metrics if available
      updateDashboardMetrics(data);
    } else {
      throw new Error('Health check failed');
    }
  } catch (error) {
    // Update status to offline
    statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Offline</span>';
    statusElement.className = 'health-status offline';

    // Display error
    resultElement.innerHTML = `
            <div class="error-result">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Error: ${error.message}</span>
            </div>
        `;
  }
}

// Display health check results
function displayHealthResult(element, data, responseTime, success) {
  if (!success) return;

  let resultHTML = `
        <div class="health-success">
            <div class="response-time">
                <i class="fas fa-clock"></i>
                <span>${responseTime}ms</span>
            </div>
    `;

  if (data.data) {
    const healthData = data.data;

    if (healthData.uptime) {
      const uptime =
        typeof healthData.uptime === 'object' ? healthData.uptime.formatted : formatUptime(healthData.uptime);
      resultHTML += `<div class="metric-item">Uptime: ${uptime}</div>`;
    }

    if (healthData.memory) {
      const memory = healthData.memory;
      const memoryUsed = memory.heapUsed || memory.used || 0;
      const memoryTotal = memory.heapTotal || memory.total || 0;
      resultHTML += `<div class="metric-item">Memory: ${memoryUsed}MB / ${memoryTotal}MB</div>`;
    }

    if (healthData.services) {
      const services = healthData.services;
      resultHTML += `<div class="metric-item">Database: ${services.database}</div>`;
      resultHTML += `<div class="metric-item">Server: ${services.server}</div>`;
    }

    if (healthData.environment) {
      const env = healthData.environment;
      resultHTML += `<div class="metric-item">Environment: ${env.nodeEnv || env}</div>`;
    }
  }

  resultHTML += '</div>';
  element.innerHTML = resultHTML;
}

// Update dashboard metrics
function updateDashboardMetrics(data) {
  if (!data.data) return;

  const healthData = data.data;

  // Update uptime
  if (healthData.uptime) {
    const uptimeElement = document.getElementById('uptime-value');
    if (uptimeElement) {
      const uptime =
        typeof healthData.uptime === 'object' ? healthData.uptime.formatted : formatUptime(healthData.uptime);
      uptimeElement.textContent = uptime;
    }
  }

  // Update memory usage
  if (healthData.memory) {
    const memoryElement = document.getElementById('memory-value');
    if (memoryElement) {
      const memory = healthData.memory;
      const used = memory.heapUsed || memory.used || 0;
      const total = memory.heapTotal || memory.total || 0;
      const percentage = total > 0 ? Math.round((used / total) * 100) : 0;
      memoryElement.textContent = `${used}MB (${percentage}%)`;
    }
  }

  // Update response time (will be set by individual calls)
  if (healthData.responseTime) {
    const responseTimeElement = document.getElementById('response-time-value');
    if (responseTimeElement) {
      responseTimeElement.textContent = `${healthData.responseTime}ms`;
    }
  }

  // Update database status
  if (healthData.services && healthData.services.database) {
    const dbStatusElement = document.getElementById('database-status-value');
    if (dbStatusElement) {
      dbStatusElement.textContent = healthData.services.database;
      dbStatusElement.className = healthData.services.database === 'connected' ? 'status-online' : 'status-offline';
    }
  }
}

// Format uptime in seconds to human readable format
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

// Refresh all health checks
async function refreshAllHealthChecks() {
  const refreshBtn = document.querySelector('.refresh-btn');
  let originalHTML = '';

  // Show loading state if button exists
  if (refreshBtn) {
    originalHTML = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;
  }

  try {
    // Test all endpoints
    await Promise.all([
      testHealthEndpoint('/api-health', 'basic-health-status', 'basic-health-result'),
      testHealthEndpoint('/api/health', 'comprehensive-health-status', 'comprehensive-health-result'),
      testHealthEndpoint('/api/health/detailed', 'detailed-health-status', 'detailed-health-result'),
    ]);
  } catch (error) {
    console.error('Error refreshing health checks:', error);
  } finally {
    // Restore button state if button exists
    if (refreshBtn) {
      setTimeout(() => {
        refreshBtn.innerHTML = originalHTML;
        refreshBtn.disabled = false;
      }, 1000);
    }
  }
}

// Show endpoint details
function showEndpointDetails(method, path, description) {
  // Create modal overlay
  const overlay = document.createElement('div');
  overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
    `;

  // Create modal content
  const modal = document.createElement('div');
  modal.style.cssText = `
        background: white;
        border-radius: 16px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        animation: modalSlideIn 0.3s ease;
    `;

  // Add CSS animation
  const style = document.createElement('style');
  style.textContent = `
        @keyframes modalSlideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
  document.head.appendChild(style);

  modal.innerHTML = `
        <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <span class="endpoint-method ${method.toLowerCase()}" style="padding: 0.5rem 1rem; border-radius: 8px; font-weight: 600; text-transform: uppercase;">${method}</span>
            <h3 style="font-family: Monaco, monospace; color: #374151; flex: 1;">${path}</h3>
        </div>
        <p style="color: #6b7280; margin-bottom: 2rem; line-height: 1.6;">${description}</p>
        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <button onclick="copyToClipboard('${path}')" style="background: #667eea; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; font-weight: 500;">
                Copy Path
            </button>
            <button onclick="this.closest('.modal-overlay').remove()" style="background: #e5e7eb; color: #374151; border: none; padding: 0.75rem 1.5rem; border-radius: 8px; cursor: pointer; font-weight: 500;">
                Close
            </button>
        </div>
    `;

  overlay.className = 'modal-overlay';
  overlay.appendChild(modal);
  document.body.appendChild(overlay);

  // Close on overlay click
  overlay.addEventListener('click', function (e) {
    if (e.target === overlay) {
      overlay.remove();
    }
  });

  // Close on escape key
  document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
      overlay.remove();
    }
  });
}

// Copy to clipboard function
function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(function () {
    // Show success message
    const toast = document.createElement('div');
    toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            z-index: 10001;
            animation: toastSlideIn 0.3s ease;
        `;
    toast.textContent = 'Copied to clipboard!';
    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
    }, 2000);
  });
}

// Animate statistics
function animateStats() {
  const stats = document.querySelectorAll('.stat-number');
  stats.forEach((stat) => {
    const target = parseInt(stat.textContent);
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        stat.textContent = target + (stat.textContent.includes('%') ? '%' : '');
        clearInterval(timer);
      } else {
        stat.textContent = Math.floor(current) + (stat.textContent.includes('%') ? '%' : '');
      }
    }, 30);
  });
}

// Add CSS for toast animation
const toastStyle = document.createElement('style');
toastStyle.textContent = `
    @keyframes toastSlideIn {
        from { opacity: 0; transform: translateX(100%); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    .health-status.checking {
        color: #f59e0b;
    }
`;
document.head.appendChild(toastStyle);
