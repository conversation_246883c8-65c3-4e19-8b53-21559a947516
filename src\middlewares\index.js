import cors from 'cors';
import helmet from 'helmet';
import express from 'express';
import cookieParser from 'cookie-parser';

import config from '../config/environment.config.js';
import { Constants } from '../constants/general.js';
import rateLimiter from './rateLimiter.js';
import httpLogger from './httpLogger.js';
import { sanitizeRequest } from './sanitizeRequest.js';

const setupMiddlewares = (app) => {
  app.use(
    cors({
      origin: config.frontend_host,
      methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
      credentials: true,
      exposedHeaders: [Constants.HEADERS.AUTHORIZATION],
      maxAge: 3600,
    })
  );

  app.use(httpLogger);
  app.use(rateLimiter(500, 5 * 60 * 1000));
  app.use(express.json({ limit: '16kb' }));
  app.use(express.urlencoded({ extended: true, limit: '16kb' }));
  app.use(cookieParser());
  app.use(sanitizeRequest);
  app.use(helmet());
};

export default setupMiddlewares;
