import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import { getDashboardOverview } from '../../controllers/dashboard.controller.js';

const router = express.Router();

// Global middleware stack: only ADMINs can access these routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   GET /api/admin/dashboard
 * @desc    Fetch dashboard overview data
 * @access  Admin only
 */
router.get('/', getDashboardOverview);

export default router;
