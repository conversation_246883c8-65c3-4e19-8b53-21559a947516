import mongoose from 'mongoose';
import { EatingPreference } from '../constants/enums.js';

const ClientSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  weight: {
    type: Number, // Weight in kg
    default: null,
  },
  current_weight: {
    type: Number, // Current weight in kg
    default: null,
  },
  height: {
    type: Number, // Height in cm
    default: null,
  },
  address: {
    type: String,
    default: null,
  },
  expected_weight: {
    type: Number,
    default: null,
  },
  eating_preference: {
    type: String,
    enum: EatingPreference.ALL,
    default: EatingPreference.VEGETARIAN,
  },
  intoxication: {
    type: [String],
    default: [],
  },
  medical_issues: {
    type: [String],
    default: [],
  },
  stress_level: {
    type: Number,
    default: null,
  },
  eating_habits: {
    breakfast: { type: String, default: null },
    lunch: { type: String, default: null },
    dinner: { type: String, default: null },
    snacks: { type: String, default: null },
  },
  description: {
    type: String,
    default: null,
  },
  goals: [
    {
      type: String, // Example: 'Lose weight', 'Gain muscle', etc.
    },
  ],
  coachId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Coach',
    default: null,
  },
  fitnessPlan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'FitnessPlan',
    default: null,
  },
  workoutHistory: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'WorkoutSession',
      default: null,
    },
  ],
  nutrition: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'NutritionPlan',
  },
  progress: {
    weight: { type: Number },
    bodyFatPercentage: { type: Number },
    measurements: [
      {
        date: { type: Date }, // Date of measurement
        weight: { type: Number },
        bodyFat: { type: Number },
        waist: { type: Number },
        chest: { type: Number },
      },
    ],
  },
});

// Indexes for optimized querying
ClientSchema.index({ userId: 1 });
ClientSchema.index({ coachId: 1 });
ClientSchema.index({ fitnessPlan: 1 });
ClientSchema.index({ workoutHistory: 1 });
ClientSchema.index({ nutrition: 1 });

export default mongoose.model('Client', ClientSchema, 'clients');
