import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  getSleepRecords,
  getSleepRecordsRange,
  updateSleepRecord,
} from '../../controllers/sleepTracking.controller.js';

const router = express.Router();

// Apply authentication and authorization middleware to all sleep tracking routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.CLIENT));

/**
 * @route   GET /api/clients/sleep-tracking
 * @desc    Get all sleep records of the authenticated client
 * @access  Client only
 */
router.get('/', getSleepRecords);

/**
 * @route   GET /api/clients/sleep-tracking/range
 * @desc    Get sleep records within a date range (query params: from, to)
 * @access  Client only
 */
router.get('/range', getSleepRecordsRange);

/**
 * @route   PATCH /api/clients/sleep-tracking
 * @desc    Update a sleep record for the authenticated client
 * @access  Client only
 */
router.patch('/', updateSleepRecord);

export default router;
