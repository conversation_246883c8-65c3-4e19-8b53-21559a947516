import express from 'express';
import validateAccessToken from '../../middlewares/validateAccessToken.js';
import authenticateJWT from '../../middlewares/authenticateJWT.js';
import authorizeRoles from '../../middlewares/authorizeRoles.js';
import { UserRoles } from '../../constants/userRoles.js';
import {
  createClientByAdmin,
  getClientsList,
  getClientById,
  updateClientByAdmin,
  deleteClientByAdmin,
  deactivateClientByAdmin,
  activateClientByAdmin,
} from '../../controllers/clients.controller.js';
import { getSleepRecordById } from '../../controllers/sleepTracking.controller.js';
import { validateMongoId } from '../../middlewares/validateMongoId.js';

const router = express.Router();

// Apply authentication and authorization middleware to all admin client routes
router.use(validateAccessToken, authenticateJWT, authorizeRoles(UserRoles.ADMIN));

/**
 * @route   POST /api/admin/clients
 * @desc    Create a new client account (admin only)
 * @access  Admin only
 */
router.post('/', createClientByAdmin);

/**
 * @route   GET /api/admin/clients
 * @desc    Get paginated list of all clients
 * @access  Admin only
 */
router.get('/', getClientsList);

/**
 * @route   GET /api/admin/clients/:id
 * @desc    Get detailed information about a specific client
 * @access  Admin only
 */
router.get('/:id', validateMongoId(), getClientById);

/**
 * @route   PUT /api/admin/clients/:id
 * @desc    Update client information (admin override)
 * @access  Admin only
 */
router.put('/:id', validateMongoId(), updateClientByAdmin);

/**
 * @route   PATCH /api/admin/clients/:id/deactivate
 * @desc    Deactivate a client account
 * @access  Admin only
 */
router.patch('/:id/deactivate', validateMongoId(), deactivateClientByAdmin);

/**
 * @route   PATCH /api/admin/clients/:id/activate
 * @desc    Activate a client account
 * @access  Admin only
 */
router.patch('/:id/activate', validateMongoId(), activateClientByAdmin);

/**
 * @route   DELETE /api/admin/clients/:id
 * @desc    Permanently delete a client account
 * @access  Admin only
 */
router.delete('/:id', validateMongoId(), deleteClientByAdmin);

/**
 * @route   GET /api/admin/clients/:id/sleep-records/:recordId
 * @desc    Get specific sleep record for any client (admin view)
 * @access  Admin only
 */
router.get('/:id/sleep-records/:recordId', validateMongoId(), getSleepRecordById);

export default router;
