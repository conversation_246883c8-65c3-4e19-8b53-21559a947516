import userSettingsModel from '../models/clientsSettings.model.js';
import { ValidationMessages } from '../constants/messages.js';
import { HttpStatus } from '../constants/httpStatus.js';
import { AppError, NotFoundError, ValidationError } from '../utils/errorHandler.js';
import userSettingsSerializer from '../serializers/userSettingsSerializer.js';

class UserSettingsService {
  static async getUserSettings(req, res) {
    const userId = req.user;

    const userSettings = await userSettingsModel.findOne({ userId });

    if (!userSettings) {
      throw new NotFoundError('User settings not found');
    }

    const formattedSettings = userSettingsSerializer(userSettings);

    return formattedSettings;
  }

  static async updateUserSettings(req, res) {
    const userId = req.user;

    const allowedFields = [
      'dailySleepGoalHours',
      'sleepReminderTime',
      'isSleepReminderOn',
      'awakeReminderTime',
      'isAwakeReminderOn',
      'dailyWaterGoalLiters',
      'waterReminderTime',
      'isWaterReminderOn',
      'dailyStepsGoal',
      'stepsReminderTime',
      'isStepsReminderOn',
    ];

    if (!req.body || Object.keys(req.body).length === 0) {
      throw new ValidationError(
        ValidationMessages.GENERAL.INVALID_REQUEST_BODY,
        HttpStatus.STATUS_CODE.UNPROCESSABLE_ENTITY
      );
    }
    const updates = {};
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }

    if (Object.keys(updates).length === 0) {
      throw new ValidationError('No valid fields provided for update', HttpStatus.STATUS_CODE.BAD_REQUEST);
    }

    const userSettings = await userSettingsModel.findOneAndUpdate({ userId }, updates, {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
      runValidators: true,
    });

    if (!userSettings) {
      throw new NotFoundError('User settings not found');
    }

    return userSettingsSerializer(userSettings);
  }
}

export default UserSettingsService;
