import dotenv from 'dotenv';
dotenv.config();

import passport from 'passport';
import config from './environment.config.js';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import logger from '../utils/logger.js';
import { ValidationMessages } from '../constants/messages.js';
import userModel from '../models/user.model.js';

const opts = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: config.jwt_access_token_secret_key,
};

passport.use(
  new JwtStrategy(opts, async function (jwt_payload, done) {
    try {
      const user = await userModel.findOne({ _id: jwt_payload._id }).select('-password');

      if (user) {
        return done(null, user);
      } else {
        return done(null, false);
      }
    } catch (error) {
      logger.error(`${chalk.cyan(ValidationMessages.TOKEN.ERROR_JWT_STRATEGY)}`, error);
      return done(error, false);
    }
  })
);
